"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_app_components_FeatureCards_jsx";
exports.ids = ["_ssr_app_components_FeatureCards_jsx"];
exports.modules = {

/***/ "(ssr)/./app/components/FeatureCards.jsx":
/*!*****************************************!*\
  !*** ./app/components/FeatureCards.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeatureCards)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../i18n/LanguageContext */ \"(ssr)/./app/i18n/LanguageContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction FeatureCards() {\n    const { isRTL } = (0,_i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useInView)(ref, {\n        once: true,\n        amount: 0.2\n    });\n    const [particleProps, setParticleProps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Memoize the features array to prevent it from recreating on every render\n    const features = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                id: 1,\n                title: \"Smart Call Routing\",\n                description: \"Directs calls based on priority and availability, ensuring important calls are never missed and are directed to the right person.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-7 w-7\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                highlights: [\n                    \"Intelligent call prioritization\",\n                    \"Customizable routing rules\",\n                    \"Automatic call distribution\"\n                ],\n                accentClass: \"bg-gradient-to-r from-indigo-500 to-blue-500\",\n                iconBgClass: \"bg-gradient-to-br from-indigo-500/60 to-blue-500/60\",\n                checkColor: \"text-white\",\n                laserColor: \"laser-particle-blue\",\n                bgClass: \"bg-gradient-to-br from-indigo-600/50 to-blue-800/50 border-indigo-400/30\",\n                titleColor: \"text-white\",\n                descriptionColor: \"text-blue-100\"\n            },\n            {\n                id: 2,\n                title: \"AI Call Summaries\",\n                description: \"Get the key points without listening to recordings. Our AI generates concise, accurate summaries of every conversation.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-7 w-7\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                highlights: [\n                    \"Automated call summaries\",\n                    \"Action item extraction\",\n                    \"Follow-up reminders\"\n                ],\n                accentClass: \"bg-gradient-to-r from-purple-500 to-indigo-500\",\n                iconBgClass: \"bg-gradient-to-br from-purple-500/60 to-indigo-500/60\",\n                checkColor: \"text-white\",\n                laserColor: \"laser-particle-purple\",\n                bgClass: \"bg-gradient-to-br from-purple-600/50 to-indigo-800/50 border-purple-400/30\",\n                titleColor: \"text-white\",\n                descriptionColor: \"text-purple-100\"\n            },\n            {\n                id: 3,\n                title: \"Voice & Text Transcription\",\n                description: \"Every call documented and searchable with high-accuracy transcription that captures even nuanced conversations.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-7 w-7\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                highlights: [\n                    \"Calendar synchronization\",\n                    \"CRM data capture\",\n                    \"Custom workflow automation\"\n                ],\n                accentClass: \"bg-gradient-to-r from-green-500 to-emerald-500\",\n                iconBgClass: \"bg-gradient-to-br from-green-500/60 to-emerald-500/60\",\n                checkColor: \"text-white\",\n                laserColor: \"laser-particle-green\",\n                bgClass: \"bg-gradient-to-br from-green-600/50 to-emerald-800/50 border-green-400/30\",\n                titleColor: \"text-white\",\n                descriptionColor: \"text-green-100\"\n            },\n            {\n                id: 4,\n                title: \"24/7 Revenue Protection\",\n                description: \"Never miss another sales opportunity. Our system works around the clock to ensure every potential customer is engaged and converted.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-7 w-7\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                highlights: [\n                    \"Always-on conversion\",\n                    \"After-hours sales capture\",\n                    \"Zero missed opportunities\"\n                ],\n                accentClass: \"bg-gradient-to-r from-pink-500 to-rose-500\",\n                iconBgClass: \"bg-gradient-to-br from-pink-500/60 to-rose-500/60\",\n                checkColor: \"text-white\",\n                laserColor: \"laser-particle-pink\",\n                bgClass: \"bg-gradient-to-br from-pink-600/50 to-rose-800/50 border-pink-400/30\",\n                titleColor: \"text-white\",\n                descriptionColor: \"text-pink-100\"\n            }\n        ], []);\n    // Generate random particle properties on client-side only\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const newParticleProps = [];\n        features.forEach((feature)=>{\n            const featureParticles = [];\n            for(let i = 0; i < 3; i++){\n                featureParticles.push({\n                    width: Math.random() * 4 + 2,\n                    height: Math.random() * 4 + 2,\n                    left: Math.random() * 80 + 10,\n                    top: Math.random() * 80 + 10,\n                    xMove: Math.random() * 40 - 20,\n                    yMove: Math.random() * 40 - 20,\n                    duration: Math.random() * 3 + 2\n                });\n            }\n            newParticleProps.push(featureParticles);\n        });\n        setParticleProps(newParticleProps);\n    }, [\n        features\n    ]);\n    // Animation variants\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const headingVariants = {\n        hidden: {\n            opacity: 0,\n            y: -20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    const cardVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: (i)=>({\n                opacity: 1,\n                y: 0,\n                transition: {\n                    delay: i * 0.1,\n                    duration: 0.5,\n                    ease: \"easeOut\"\n                }\n            }),\n        hover: {\n            y: -8,\n            scale: 1.03,\n            boxShadow: \"0 10px 25px rgba(80, 60, 200, 0.2)\",\n            transition: {\n                type: \"spring\",\n                stiffness: 400,\n                damping: 10\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        ref: ref,\n        className: \"py-10 relative overflow-hidden neon-bg-glow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-1/4 w-1/3 h-1/3 bg-purple-900/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-blue-900/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container max-w-7xl mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"text-center mb-12\",\n                        initial: \"hidden\",\n                        animate: isInView ? \"visible\" : \"hidden\",\n                        variants: headingVariants,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"heading-lg laser-gradient-text mb-4\",\n                                \"data-text\": \"Revolutionary AI Voice & Text\",\n                                children: \"Revolutionary AI Voice & Text\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"subheading-text max-w-3xl mx-auto\",\n                                children: [\n                                    \"Transform how your business handles communication with AI that actually \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-purple-400 font-medium\",\n                                        children: \"talks and listens\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 85\n                                    }, this),\n                                    \" to your customers—not just responds to texts. Save time, increase revenue, and deliver exceptional service 24/7.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 features-grid\",\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: isInView ? \"visible\" : \"hidden\",\n                        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                custom: index,\n                                variants: cardVariants,\n                                whileHover: \"hover\",\n                                className: `laser-card p-6 rounded-xl relative group min-h-[320px] flex flex-col ${feature.bgClass} border border-opacity-30 shadow-[0_0_15px_rgba(80,60,200,0.15)] backdrop-blur-md`,\n                                role: \"article\",\n                                \"aria-labelledby\": `feature-title-${feature.id}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                        className: \"absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                        animate: {\n                                            boxShadow: [\n                                                \"0 0 0px rgba(128, 0, 255, 0)\",\n                                                \"0 0 15px rgba(128, 0, 255, 0.3)\",\n                                                \"0 0 0px rgba(128, 0, 255, 0)\"\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            repeat: Infinity,\n                                            ease: \"easeInOut\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 overflow-hidden rounded-xl pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                        children: particleProps[index]?.map((particle, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: `absolute rounded-full ${feature.laserColor}`,\n                                                style: {\n                                                    width: `${particle.width}px`,\n                                                    height: `${particle.height}px`,\n                                                    left: `${particle.left}%`,\n                                                    top: `${particle.top}%`,\n                                                    opacity: 0.7,\n                                                    filter: \"blur(2px)\"\n                                                },\n                                                animate: {\n                                                    x: [\n                                                        0,\n                                                        particle.xMove\n                                                    ],\n                                                    y: [\n                                                        0,\n                                                        particle.yMove\n                                                    ],\n                                                    opacity: [\n                                                        0.7,\n                                                        0.9,\n                                                        0.7\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: particle.duration,\n                                                    repeat: Infinity,\n                                                    repeatType: \"reverse\",\n                                                    ease: \"easeInOut\"\n                                                }\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `p-2 rounded-lg ${feature.iconBgClass} mr-3 laser-icon`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    className: \"text-white\",\n                                                    whileHover: {\n                                                        scale: 1.1,\n                                                        rotate: 5\n                                                    },\n                                                    transition: {\n                                                        type: \"spring\",\n                                                        stiffness: 300,\n                                                        damping: 10\n                                                    },\n                                                    children: feature.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                id: `feature-title-${feature.id}`,\n                                                className: `text-xl font-bold ${feature.titleColor} group-hover:text-white transition-colors duration-300 pt-1`,\n                                                children: feature.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `text-base ${feature.descriptionColor} mb-5 leading-relaxed`,\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2.5 mt-auto\",\n                                        children: feature.highlights.map((highlight, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center text-sm feature-highlight py-1.5 px-2.5 rounded-md bg-white/5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                                        className: `mr-2.5 ${feature.checkColor} flex-shrink-0`,\n                                                        whileHover: {\n                                                            scale: 1.2,\n                                                            rotate: 5\n                                                        },\n                                                        style: {\n                                                            animationDelay: `${i * 0.5}s`\n                                                        },\n                                                        \"aria-hidden\": \"true\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: highlight\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `h-1 w-16 mt-4 rounded-full ${feature.accentClass} relative overflow-hidden`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute inset-0\",\n                                                animate: {\n                                                    boxShadow: [\n                                                        \"0 0 0px rgba(128, 0, 255, 0)\",\n                                                        \"0 0 10px rgba(128, 0, 255, 0.5)\",\n                                                        \"0 0 0px rgba(128, 0, 255, 0)\"\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity,\n                                                    ease: \"easeInOut\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute inset-0 bg-white/10\",\n                                                animate: {\n                                                    x: [\n                                                        \"-100%\",\n                                                        \"100%\"\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 1.5,\n                                                    repeat: Infinity,\n                                                    ease: \"easeInOut\",\n                                                    repeatDelay: 1\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"laser-divider mt-10 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/FeatureCards.jsx\n");

/***/ })

};
;