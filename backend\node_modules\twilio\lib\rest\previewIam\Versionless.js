"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const organization_1 = require("./versionless/organization");
class Versionless extends Version_1.default {
    /**
     * Initialize the Versionless version of PreviewIam
     *
     * @param domain - The Twilio (Twilio.PreviewIam) domain
     */
    constructor(domain) {
        super(domain, "Organizations");
    }
    /** Getter for organization resource */
    get organization() {
        this._organization = this._organization || (0, organization_1.OrganizationListInstance)(this);
        return this._organization;
    }
}
exports.default = Versionless;
