"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Routes
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const phoneNumber_1 = require("./v2/phoneNumber");
const sipDomain_1 = require("./v2/sipDomain");
const trunk_1 = require("./v2/trunk");
class V2 extends Version_1.default {
    /**
     * Initialize the V2 version of Routes
     *
     * @param domain - The Twilio (Twilio.Routes) domain
     */
    constructor(domain) {
        super(domain, "v2");
    }
    /** Getter for phoneNumbers resource */
    get phoneNumbers() {
        this._phoneNumbers = this._phoneNumbers || (0, phoneNumber_1.PhoneNumberListInstance)(this);
        return this._phoneNumbers;
    }
    /** Getter for sipDomains resource */
    get sipDomains() {
        this._sipDomains = this._sipDomains || (0, sipDomain_1.SipDomainListInstance)(this);
        return this._sipDomains;
    }
    /** Getter for trunks resource */
    get trunks() {
        this._trunks = this._trunks || (0, trunk_1.TrunkListInstance)(this);
        return this._trunks;
    }
}
exports.default = V2;
