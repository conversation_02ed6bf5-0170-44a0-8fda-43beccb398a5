"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPage = exports.UserListInstance = exports.UserInstance = exports.UserContextImpl = exports.ScimUser = exports.ScimName = exports.ScimMeta = exports.ScimEmailAddress = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
/**
 * Email address list of the user. Primary email must be defined if there are more than 1 email. Primary email must match the username.
 */
class ScimEmailAddress {
}
exports.ScimEmailAddress = ScimEmailAddress;
/**
 * Meta
 */
class ScimMeta {
}
exports.ScimMeta = ScimMeta;
/**
 * User\'s name
 */
class ScimName {
}
exports.ScimName = ScimName;
class ScimUser {
}
exports.ScimUser = ScimUser;
class UserContextImpl {
    constructor(_version, organizationSid, id) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(organizationSid)) {
            throw new Error("Parameter 'organizationSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(id)) {
            throw new Error("Parameter 'id' is not valid.");
        }
        this._solution = { organizationSid, id };
        this._uri = `/${organizationSid}/scim/Users/<USER>
    }
    remove(callback) {
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
            headers,
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/scim+json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UserInstance(operationVersion, payload, instance._solution.organizationSid, instance._solution.id));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, headers, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        let data = {};
        data = params;
        if (headers === null || headers === undefined) {
            headers = {};
        }
        headers["Content-Type"] = "application/json";
        headers["Accept"] = "application/scim+json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "put",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UserInstance(operationVersion, payload, instance._solution.organizationSid, instance._solution.id));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UserContextImpl = UserContextImpl;
class UserInstance {
    constructor(_version, payload, organizationSid, id) {
        this._version = _version;
        this.id = payload.id;
        this.externalId = payload.externalId;
        this.userName = payload.userName;
        this.displayName = payload.displayName;
        this.name = payload.name;
        this.emails = payload.emails;
        this.active = payload.active;
        this.locale = payload.locale;
        this.timezone = payload.timezone;
        this.schemas = payload.schemas;
        this.meta = payload.meta;
        this.detail = payload.detail;
        this.scimType = payload.scimType;
        this.status = payload.status;
        this.code = payload.code;
        this.moreInfo = payload.moreInfo;
        this._solution = { organizationSid, id: id || this.id };
    }
    get _proxy() {
        this._context =
            this._context ||
                new UserContextImpl(this._version, this._solution.organizationSid, this._solution.id);
        return this._context;
    }
    /**
     * Remove a UserInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a UserInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed UserInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            id: this.id,
            externalId: this.externalId,
            userName: this.userName,
            displayName: this.displayName,
            name: this.name,
            emails: this.emails,
            active: this.active,
            locale: this.locale,
            timezone: this.timezone,
            schemas: this.schemas,
            meta: this.meta,
            detail: this.detail,
            scimType: this.scimType,
            status: this.status,
            code: this.code,
            moreInfo: this.moreInfo,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UserInstance = UserInstance;
function UserListInstance(version, organizationSid) {
    if (!(0, utility_1.isValidPathParam)(organizationSid)) {
        throw new Error("Parameter 'organizationSid' is not valid.");
    }
    const instance = ((id) => instance.get(id));
    instance.get = function get(id) {
        return new UserContextImpl(version, organizationSid, id);
    };
    instance._version = version;
    instance._solution = { organizationSid };
    instance._uri = `/${organizationSid}/scim/Users`;
    instance.create = function create(params, headers, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        let data = {};
        data = params;
        if (headers === null || headers === undefined) {
            headers = {};
        }
        headers["Content-Type"] = "application/json";
        headers["Accept"] = "application/scim+json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UserInstance(operationVersion, payload, instance._solution.organizationSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["filter"] !== undefined)
            data["filter"] = params["filter"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/scim+json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new UserPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new UserPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.UserListInstance = UserListInstance;
class UserPage extends Page_1.default {
    /**
     * Initialize the UserPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of UserInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new UserInstance(this._version, payload, this._solution.organizationSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.UserPage = UserPage;
