"use client";

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Move callSteps outside component to prevent recreation on every render
const callSteps = [
  '☎️ Missed Call {phoneNumber} — 1 minute ago', // Step 0
  '🔔 (Potential Customer) 🔔', // Step 1
  '⚡ CallSaver AI Assistant Activated via Call Forwarding...', // Step 2
  '🤖 AI:', // Step 3
  '"Hi! We noticed you just called. The person you\'re trying to reach is currently unavailable.\n\nI\'m their AI assistant — how can I help you today?\nWould you like to schedule an appointment or ask a quick question?"', // Step 4 (AI msg)
  '🧑 Customer:', // Step 5
  '"Hey, this is <PERSON>. I wanted to check if Mr. XXX is available tomorrow at 8 PM.\nCan you book that appointment for me?"', // Step 6 (Customer msg)
  '🤖 AI:', // Step 7
  '✅ Done!' // Step 8 (AI msg/status)
];

const CallPreviewBox = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [typingComplete, setTypingComplete] = useState(false);
  const [callHandled, setCallHandled] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('(*************');
  const [isMounted, setIsMounted] = useState(false);
  const [isBrowser, setIsBrowser] = useState(false);
  const componentRef = useRef(null);

  // Check if we're in browser environment
  useEffect(() => {
    setIsBrowser(typeof window !== 'undefined');
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Rotate demo phone numbers only after mount and in browser
  useEffect(() => {
    if (!isMounted || !isBrowser) return;
    
    const phoneNumbers = [
      '(*************',
      '(*************',
      '(*************'
    ];
    
    let currentIndex = 0;
    const interval = setInterval(() => {
      currentIndex = (currentIndex + 1) % phoneNumbers.length;
      setPhoneNumber(phoneNumbers[currentIndex]);
    }, 7000); // Change every 7 seconds
    
    return () => clearInterval(interval);
  }, [isMounted, isBrowser]);

  // Function to get the correct step text with dynamic phone number
  const getStepText = (index) => {
    if (index === 0) {
      return callSteps[0].replace('{phoneNumber}', phoneNumber);
    }
    return callSteps[index];
  };

  // Combined effect for typing and step progression
  useEffect(() => {
    if (!isMounted || !isBrowser) return;

    const currentStepContent = getStepText(currentStep);
    const isUserMessage = currentStep === 6;
    const typingDelay = isUserMessage
      ? 2000
      : (currentStepContent?.length || 0) * 15;
    const minDelay = isUserMessage ? 2000 : 800;

    // Reset typing completion status for the new step
    setTypingComplete(false);

    const typingTimer = setTimeout(() => {
      setTypingComplete(true); // Mark typing as complete for current step

      // After typing is complete, proceed to the next step or mark call as handled
      if (currentStep < callSteps.length - 1) {
        const nextStepDelay = 1200; // Pause between messages
        const nextStepTimer = setTimeout(() => {
          setCurrentStep(currentStep + 1);
        }, nextStepDelay);
        return () => clearTimeout(nextStepTimer);
      } else if (currentStep === callSteps.length - 1 && !callHandled) {
        const successDelay = 1500;
        const successTimer = setTimeout(() => {
          setCallHandled(true);
        }, successDelay);
        return () => clearTimeout(successTimer);
      }
    }, Math.max(typingDelay, minDelay));

    return () => clearTimeout(typingTimer);
  }, [currentStep, isMounted, isBrowser, callHandled, phoneNumber]);

  // If not mounted or not in browser, return a placeholder
  if (!isMounted || !isBrowser) {
    return (
      <div className="relative border-2 border-purple-500/50 bg-gray-900/80 rounded-2xl overflow-hidden shadow-[0_0_40px_rgba(139,92,246,0.6)] backdrop-blur-sm w-full max-w-sm mx-auto sm:max-w-md lg:max-w-[420px] aspect-[9/19] sm:aspect-[9/16]">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-12 h-12 rounded-full border-t-2 border-b-2 border-purple-500 animate-spin"></div>
        </div>
      </div>
    );
  }

  // Pulsing emoji animation for special characters
  const pulseEmoji = (text, isPrimary = false) => {
    // For emoji at the start of the text
    if (text.startsWith('☎️') || text.startsWith('⚡') || text.startsWith('🟣') ||
        text.startsWith('🤖') || text.startsWith('🧑') || text.startsWith('🔔')) { // Added bell
      return (
        <>
          <motion.span
            animate={{ scale: [1, 1.2, 1] }} 
            transition={{ duration: 1.5, repeat: Infinity, repeatType: 'loop' }}
            className="inline-block mr-1"
          >
            {text.substring(0, 2)}
          </motion.span>
            {text.substring(2)}
        </>
      );
    }

    // Handle ending bell emoji for "Potential Customer"
    if (text.includes('(Potential Customer) 🔔')) {
      const beforeBell = text.substring(0, text.indexOf('🔔'));
      const bell = '🔔';
      return (
        <>
          {beforeBell}
          <motion.span
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1.5, repeat: Infinity, repeatType: 'loop' }}
            className="inline-block ml-1" // Adjust spacing if needed
          >
            {bell}
          </motion.span>
        </>
      );
    }

    // Special handling for "✅ Done!" with bounce animation
    if (text === '✅ Done!' && isPrimary) { // Check for exact match now
      const checkAndDone = '✅ Done!';
      
      return (
        <>
          {/* {beforeCheck && beforeCheck} */}
          <motion.span
            initial={{ scale: 0.8 }}
            animate={{ scale: [0.8, 1.2, 1] }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="inline-block text-green-400 font-bold"
          >
            {checkAndDone}
          </motion.span>
          {/* {afterDone && afterDone} */}
        </>
      );
    }

    return text;
  };

  // Function to format text with line breaks
  const formatText = (text) => {
    if (!text) return null;
    
    // Ensure text is a string before splitting
    if (typeof text !== 'string') {
      return text; // Return as is if not a string
    }
    
    // Replace \n with line breaks for proper display
    return text.split('\n').map((line, i) => (
      <span key={i} className="block">
        {i > 0 && <br />}
        {line}
      </span>
    ));
  };

  // Base container animation
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6, 
        ease: "easeOut" 
      }
    }
  };

  // Message animation variants
  const messageVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  // Function to determine message bubble styles based on sender - Adjusted indices
  const getMessageStyles = (index) => {
    // AI message styles (indices 3, 4, 7, 8)
    if (index === 3 || index === 4 || index === 7 || index === 8) {
      return "bg-purple-900/40 border border-purple-500/30 backdrop-blur-sm shadow-sm ml-4";
    }
    // Customer message styles (indices 5, 6)
    else if (index === 5 || index === 6) {
      return "bg-blue-900/40 border border-blue-500/30 backdrop-blur-sm shadow-sm ml-0 mr-4";
    }
    // System message styles (indices 0-2)
    return "bg-transparent";
  };

  // Determine if message is primary content (for special formatting) - Adjusted indices
  const isMessagePrimary = (index) => {
    // Indices of main message content: AI msg (4), Customer msg (6), Done! (8)
    return index === 4 || index === 6 || index === 8;
  };

  return (
    <motion.div 
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      ref={componentRef}
      className="relative border-2 border-purple-500/50 bg-gray-900/80 rounded-2xl overflow-hidden shadow-[0_0_40px_rgba(139,92,246,0.6)] backdrop-blur-sm w-full max-w-sm mx-auto sm:max-w-md lg:max-w-[420px]"
    >
      {/* Add pulsing outer glow */}
      <motion.div 
        className="absolute inset-0 -z-10 rounded-2xl"
        animate={{
          boxShadow: [
            '0 0 20px rgba(139, 92, 246, 0.3)',
            '0 0 30px rgba(139, 92, 246, 0.6)',
            '0 0 20px rgba(139, 92, 246, 0.3)'
          ]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      
      <div className="aspect-[9/19] sm:aspect-[9/16] w-full">
        {/* Enhanced Ambient Glow Effects */}
        <div className="absolute inset-0 -z-10 rounded-3xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur-2xl transform scale-125 animate-pulse-slow"></div>
        <div className="absolute -top-10 -right-10 w-32 h-32 rounded-full bg-blue-500/10 blur-xl animate-pulse-slow"></div>
        <div className="absolute -bottom-10 -left-10 w-32 h-32 rounded-full bg-purple-500/10 blur-xl animate-pulse-slow-delay-1"></div>
        
        {/* Content Container */}
        <div className="absolute inset-0 p-4 sm:p-6 flex flex-col">
          {/* Header with improved contrast */}
          <div className="flex justify-between items-center mb-4 pb-3 border-b border-purple-500/40">
            <h3 className="text-base font-semibold text-white">Callsaver AI Assistant</h3>
            <div className="flex items-center">
              <motion.div 
                className="w-2.5 h-2.5 rounded-full bg-green-400 mr-1.5"
                animate={{
                  boxShadow: ['0 0 0px rgba(74, 222, 128, 0)', '0 0 8px rgba(74, 222, 128, 0.7)', '0 0 0px rgba(74, 222, 128, 0)']
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <span className="text-xs font-medium text-green-400">ACTIVE</span>
            </div>
          </div>
          
          {/* Subtitle with improved visibility */}
          <p className="text-sm text-purple-300 mb-3 text-center font-medium bg-purple-900/20 rounded-full py-1 px-3 backdrop-blur-sm">
            Incoming Call Simulation
          </p>
          
          {/* Call Flow Container with improved contrast */}
          <div className="flex-1 overflow-hidden bg-gradient-to-br from-purple-800/30 via-blue-800/20 to-transparent backdrop-blur-md rounded-2xl border border-purple-500/30 p-4 sm:p-5 mb-4 relative shadow-inner">
            <div className="h-full flex flex-col">
              {/* Display completed steps with better spacing and visibility */}
              <div className="flex-1 overflow-y-auto space-y-3 text-sm sm:text-base">
                {/* Completed messages */}
                {Array.from({ length: currentStep }).map((_, index) => {
                  const stepText = getStepText(index); // Use helper function
                  return (
                  <motion.div
                    key={index}
                    initial="hidden"
                    animate="visible"
                    variants={messageVariants}
                    className={`text-white p-2 rounded ${getMessageStyles(index)}`}
                  >
                    {isMessagePrimary(index)
                      ? formatText(pulseEmoji(stepText, true)) // Use stepText
                      : pulseEmoji(stepText)} {/* Use stepText */}
                  </motion.div>
                );
                })}

                {/* Current typing message with improved visibility */}
                {currentStep < callSteps.length && (
                  <motion.div 
                    initial="hidden"
                    animate="visible"
                    variants={messageVariants}
                    className={`text-white font-medium p-2 rounded ${getMessageStyles(currentStep)}`}
                  >
                    {/* Show typing indicator for user messages */}
                    {currentStep === 6 && !typingComplete ? ( // Index 6 is Customer message
                      <div className="flex items-center">
                        <span>🧑 Customer:</span>
                        <div className="ml-2 flex space-x-1">
                          <motion.div
                            animate={{ y: [0, -3, 0] }}
                            transition={{ duration: 0.6, repeat: Infinity, repeatType: 'loop', delay: 0 }}
                            className="w-2 h-2 bg-blue-400 rounded-full"
                          />
                          <motion.div
                            animate={{ y: [0, -3, 0] }}
                            transition={{ duration: 0.6, repeat: Infinity, repeatType: 'loop', delay: 0.2 }}
                            className="w-2 h-2 bg-blue-400 rounded-full"
                          />
                          <motion.div
                            animate={{ y: [0, -3, 0] }}
                            transition={{ duration: 0.6, repeat: Infinity, repeatType: 'loop', delay: 0.4 }}
                            className="w-2 h-2 bg-blue-400 rounded-full"
                          />
                        </div>
                      </div>
                    ) : (
                      isMessagePrimary(currentStep) 
                        ? formatText(pulseEmoji(getStepText(currentStep), true))
                        : pulseEmoji(getStepText(currentStep))
                    )}
                    
                    {/* Blinking cursor for typing animation */}
                    {!typingComplete && currentStep !== 6 && ( // Don't show cursor during user typing indicator
                      <motion.span
                        animate={{ opacity: [1, 0] }}
                        transition={{ duration: 0.8, repeat: Infinity, repeatType: 'reverse' }}
                        className="inline-block ml-1 w-2 h-4 bg-purple-400"
                      />
                    )}
                  </motion.div>
                )}
              </div>
            </div>
          </div>
          
          {/* Call Status Animation with improved visibility */}
          <div className="mb-2">
            {callHandled && (
              <motion.div 
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="bg-green-600/30 border border-green-500/40 rounded-xl px-3 py-2 flex flex-col items-center justify-center shadow-md shadow-green-900/20 mb-1"
              >
                <div className="flex items-center">
                  <motion.div 
                    initial={{ scale: 0.5 }}
                    animate={{ scale: [0.5, 1.2, 1] }}
                    transition={{ duration: 0.5 }}
                    className="w-5 h-5 bg-green-500 rounded-full mr-2 flex items-center justify-center text-white"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </motion.div>
                  <span className="text-sm font-medium text-green-400">Call Handled Successfully</span>
                </div>
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="flex items-center mt-1"
                >
                  <span className="text-xs text-green-300">No Missed Opportunities</span>
                  <motion.span
                    animate={{ 
                      rotate: [0, 15, -15, 0], 
                      scale: [1, 1.1, 1] 
                    }}
                    transition={{ 
                      duration: 1.5, 
                      repeat: Infinity, 
                      repeatType: 'loop',
                      delay: 1
                    }}
                    className="ml-1"
                  >
                    🚀
                  </motion.span>
                </motion.div>
              </motion.div>
            )}
            
            {/* Voice assistant animation with improved visibility */}
            {!callHandled && (
              <div className="relative flex justify-center">
                <div className="w-20 h-20 flex items-center justify-center relative">
                  {/* Pulsing outer circles with better visibility */}
                  <motion.div 
                    className="absolute inset-0 rounded-full border-2 border-purple-500/50"
                    animate={{ 
                      scale: [1, 1.2, 1],
                      opacity: [0.7, 0, 0.7]
                    }}
                    transition={{ 
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <motion.div 
                    className="absolute inset-0 rounded-full border-2 border-purple-500/50"
                    animate={{ 
                      scale: [1, 1.5, 1],
                      opacity: [0.5, 0, 0.5] 
                    }}
                    transition={{ 
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 0.5
                    }}
                  />
                  
                  {/* Circular ring with improved visibility */}
                  <motion.div 
                    className="absolute inset-2 rounded-full border-2 border-purple-500/80"
                    animate={{ rotate: 360 }}
                    transition={{ 
                      duration: 8,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                  >
                    {/* Dot moving around the circle */}
                    <motion.div
                      className="absolute w-2.5 h-2.5 rounded-full bg-purple-500 shadow-sm shadow-purple-900/50 top-0 left-1/2 -translate-x-1/2 -translate-y-1/2"
                    />
                  </motion.div>
                  
                  {/* Center circle with improved glow */}
                  <motion.div 
                    className="w-10 h-10 rounded-full bg-purple-600/40 flex items-center justify-center shadow-inner"
                    animate={{ 
                      boxShadow: ['0 0 0px rgba(147, 51, 234, 0)', '0 0 15px rgba(147, 51, 234, 0.5)', '0 0 0px rgba(147, 51, 234, 0)']
                    }}
                    transition={{ 
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 017.07 0m-9.9-2.83a9 9 0 0112.73 0" />
                    </svg>
                  </motion.div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default CallPreviewBox;
