"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_CallPreviewBox_jsx"],{

/***/ "(app-pages-browser)/./app/components/CallPreviewBox.jsx":
/*!*******************************************!*\
  !*** ./app/components/CallPreviewBox.jsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Move callSteps outside component to prevent recreation on every render\nconst callSteps = [\n    \"☎️ Missed Call {phoneNumber} — 1 minute ago\",\n    \"\\uD83D\\uDD14 (Potential Customer) \\uD83D\\uDD14\",\n    \"⚡ CallSaver AI Assistant Activated via Call Forwarding...\",\n    \"\\uD83E\\uDD16 AI:\",\n    \"\\\"Hi! We noticed you just called. The person you're trying to reach is currently unavailable.\\n\\nI'm their AI assistant — how can I help you today?\\nWould you like to schedule an appointment or ask a quick question?\\\"\",\n    \"\\uD83E\\uDDD1 Customer:\",\n    '\"Hey, this is John. I wanted to check if Mr. XXX is available tomorrow at 8 PM.\\nCan you book that appointment for me?\"',\n    \"\\uD83E\\uDD16 AI:\",\n    \"✅ Done!\" // Step 8 (AI msg/status)\n];\nconst CallPreviewBox = ()=>{\n    var _this = undefined;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [typingComplete, setTypingComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [callHandled, setCallHandled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [phoneNumber, setPhoneNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"(*************\");\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBrowser, setIsBrowser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const componentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Check if we're in browser environment\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsBrowser(\"object\" !== \"undefined\");\n        setIsMounted(true);\n        return ()=>setIsMounted(false);\n    }, []);\n    // Rotate demo phone numbers only after mount and in browser\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isMounted || !isBrowser) return;\n        const phoneNumbers = [\n            \"(*************\",\n            \"(*************\",\n            \"(*************\"\n        ];\n        let currentIndex = 0;\n        const interval = setInterval(()=>{\n            currentIndex = (currentIndex + 1) % phoneNumbers.length;\n            setPhoneNumber(phoneNumbers[currentIndex]);\n        }, 7000); // Change every 7 seconds\n        return ()=>clearInterval(interval);\n    }, [\n        isMounted,\n        isBrowser\n    ]);\n    // Function to get the correct step text with dynamic phone number\n    const getStepText = (index)=>{\n        if (index === 0) {\n            return callSteps[0].replace(\"{phoneNumber}\", phoneNumber);\n        }\n        return callSteps[index];\n    };\n    // Combined effect for typing and step progression\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isMounted || !isBrowser) return;\n        const currentStepContent = getStepText(currentStep);\n        const isUserMessage = currentStep === 6;\n        const typingDelay = isUserMessage ? 2000 : ((currentStepContent === null || currentStepContent === void 0 ? void 0 : currentStepContent.length) || 0) * 15;\n        const minDelay = isUserMessage ? 2000 : 800;\n        // Reset typing completion status for the new step\n        setTypingComplete(false);\n        const typingTimer = setTimeout(()=>{\n            setTypingComplete(true); // Mark typing as complete for current step\n            // After typing is complete, proceed to the next step or mark call as handled\n            if (currentStep < callSteps.length - 1) {\n                const nextStepDelay = 1200; // Pause between messages\n                const nextStepTimer = setTimeout(()=>{\n                    setCurrentStep(currentStep + 1);\n                }, nextStepDelay);\n                return ()=>clearTimeout(nextStepTimer);\n            } else if (currentStep === callSteps.length - 1 && !callHandled) {\n                const successDelay = 1500;\n                const successTimer = setTimeout(()=>{\n                    setCallHandled(true);\n                }, successDelay);\n                return ()=>clearTimeout(successTimer);\n            }\n        }, Math.max(typingDelay, minDelay));\n        return ()=>clearTimeout(typingTimer);\n    }, [\n        currentStep,\n        isMounted,\n        isBrowser,\n        callHandled,\n        phoneNumber\n    ]);\n    // If not mounted or not in browser, return a placeholder\n    if (!isMounted || !isBrowser) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative border-2 border-purple-500/50 bg-gray-900/80 rounded-2xl overflow-hidden shadow-[0_0_40px_rgba(139,92,246,0.6)] backdrop-blur-sm w-full max-w-sm mx-auto sm:max-w-md lg:max-w-[420px] aspect-[9/19] sm:aspect-[9/16]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-12 h-12 rounded-full border-t-2 border-b-2 border-purple-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Pulsing emoji animation for special characters\n    const pulseEmoji = function(text) {\n        let isPrimary = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // For emoji at the start of the text\n        if (text.startsWith(\"☎️\") || text.startsWith(\"⚡\") || text.startsWith(\"\\uD83D\\uDFE3\") || text.startsWith(\"\\uD83E\\uDD16\") || text.startsWith(\"\\uD83E\\uDDD1\") || text.startsWith(\"\\uD83D\\uDD14\")) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 1.5,\n                            repeat: Infinity,\n                            repeatType: \"loop\"\n                        },\n                        className: \"inline-block mr-1\",\n                        children: text.substring(0, 2)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, _this),\n                    text.substring(2)\n                ]\n            }, void 0, true);\n        }\n        // Handle ending bell emoji for \"Potential Customer\"\n        if (text.includes(\"(Potential Customer) \\uD83D\\uDD14\")) {\n            const beforeBell = text.substring(0, text.indexOf(\"\\uD83D\\uDD14\"));\n            const bell = \"\\uD83D\\uDD14\";\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    beforeBell,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 1.5,\n                            repeat: Infinity,\n                            repeatType: \"loop\"\n                        },\n                        className: \"inline-block ml-1\" // Adjust spacing if needed\n                        ,\n                        children: bell\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, _this)\n                ]\n            }, void 0, true);\n        }\n        // Special handling for \"✅ Done!\" with bounce animation\n        if (text === \"✅ Done!\" && isPrimary) {\n            const checkAndDone = \"✅ Done!\";\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                    initial: {\n                        scale: 0.8\n                    },\n                    animate: {\n                        scale: [\n                            0.8,\n                            1.2,\n                            1\n                        ]\n                    },\n                    transition: {\n                        duration: 0.6,\n                        ease: \"easeOut\"\n                    },\n                    className: \"inline-block text-green-400 font-bold\",\n                    children: checkAndDone\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, _this)\n            }, void 0, false);\n        }\n        return text;\n    };\n    // Function to format text with line breaks\n    const formatText = (text)=>{\n        if (!text) return null;\n        // Ensure text is a string before splitting\n        if (typeof text !== \"string\") {\n            return text; // Return as is if not a string\n        }\n        // Replace \\n with line breaks for proper display\n        return text.split(\"\\n\").map((line, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"block\",\n                children: [\n                    i > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 181,\n                        columnNumber: 19\n                    }, undefined),\n                    line\n                ]\n            }, i, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, undefined));\n    };\n    // Base container animation\n    const containerVariants = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    // Message animation variants\n    const messageVariants = {\n        hidden: {\n            opacity: 0,\n            y: 10\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.4,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    // Function to determine message bubble styles based on sender - Adjusted indices\n    const getMessageStyles = (index)=>{\n        // AI message styles (indices 3, 4, 7, 8)\n        if (index === 3 || index === 4 || index === 7 || index === 8) {\n            return \"bg-purple-900/40 border border-purple-500/30 backdrop-blur-sm shadow-sm ml-4\";\n        } else if (index === 5 || index === 6) {\n            return \"bg-blue-900/40 border border-blue-500/30 backdrop-blur-sm shadow-sm ml-0 mr-4\";\n        }\n        // System message styles (indices 0-2)\n        return \"bg-transparent\";\n    };\n    // Determine if message is primary content (for special formatting) - Adjusted indices\n    const isMessagePrimary = (index)=>{\n        // Indices of main message content: AI msg (4), Customer msg (6), Done! (8)\n        return index === 4 || index === 6 || index === 8;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        initial: \"hidden\",\n        animate: \"visible\",\n        variants: containerVariants,\n        ref: componentRef,\n        className: \"relative border-2 border-purple-500/50 bg-gray-900/80 rounded-2xl overflow-hidden shadow-[0_0_40px_rgba(139,92,246,0.6)] backdrop-blur-sm w-full max-w-sm mx-auto sm:max-w-md lg:max-w-[420px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute inset-0 -z-10 rounded-2xl\",\n                animate: {\n                    boxShadow: [\n                        \"0 0 20px rgba(139, 92, 246, 0.3)\",\n                        \"0 0 30px rgba(139, 92, 246, 0.6)\",\n                        \"0 0 20px rgba(139, 92, 246, 0.3)\"\n                    ]\n                },\n                transition: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"aspect-[9/19] sm:aspect-[9/16] w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10 rounded-3xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur-2xl transform scale-125 animate-pulse-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-10 -right-10 w-32 h-32 rounded-full bg-blue-500/10 blur-xl animate-pulse-slow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-10 -left-10 w-32 h-32 rounded-full bg-purple-500/10 blur-xl animate-pulse-slow-delay-1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 p-4 sm:p-6 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4 pb-3 border-b border-purple-500/40\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-base font-semibold text-white\",\n                                        children: \"Callsaver AI Assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                className: \"w-2.5 h-2.5 rounded-full bg-green-400 mr-1.5\",\n                                                animate: {\n                                                    boxShadow: [\n                                                        \"0 0 0px rgba(74, 222, 128, 0)\",\n                                                        \"0 0 8px rgba(74, 222, 128, 0.7)\",\n                                                        \"0 0 0px rgba(74, 222, 128, 0)\"\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity,\n                                                    ease: \"easeInOut\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs font-medium text-green-400\",\n                                                children: \"ACTIVE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-purple-300 mb-3 text-center font-medium bg-purple-900/20 rounded-full py-1 px-3 backdrop-blur-sm\",\n                                children: \"Incoming Call Simulation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-hidden bg-gradient-to-br from-purple-800/30 via-blue-800/20 to-transparent backdrop-blur-md rounded-2xl border border-purple-500/30 p-4 sm:p-5 mb-4 relative shadow-inner\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex flex-col\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 overflow-y-auto space-y-3 text-sm sm:text-base\",\n                                        children: [\n                                            Array.from({\n                                                length: currentStep\n                                            }).map((_, index)=>{\n                                                const stepText = getStepText(index); // Use helper function\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    initial: \"hidden\",\n                                                    animate: \"visible\",\n                                                    variants: messageVariants,\n                                                    className: \"text-white p-2 rounded \".concat(getMessageStyles(index)),\n                                                    children: [\n                                                        isMessagePrimary(index) ? formatText(pulseEmoji(stepText, true)) // Use stepText\n                                                         : pulseEmoji(stepText),\n                                                        \" \"\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, undefined);\n                                            }),\n                                            currentStep < callSteps.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                initial: \"hidden\",\n                                                animate: \"visible\",\n                                                variants: messageVariants,\n                                                className: \"text-white font-medium p-2 rounded \".concat(getMessageStyles(currentStep)),\n                                                children: [\n                                                    currentStep === 6 && !typingComplete ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83E\\uDDD1 Customer:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-2 flex space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                                        animate: {\n                                                                            y: [\n                                                                                0,\n                                                                                -3,\n                                                                                0\n                                                                            ]\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 0.6,\n                                                                            repeat: Infinity,\n                                                                            repeatType: \"loop\",\n                                                                            delay: 0\n                                                                        },\n                                                                        className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                                        animate: {\n                                                                            y: [\n                                                                                0,\n                                                                                -3,\n                                                                                0\n                                                                            ]\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 0.6,\n                                                                            repeat: Infinity,\n                                                                            repeatType: \"loop\",\n                                                                            delay: 0.2\n                                                                        },\n                                                                        className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                                        animate: {\n                                                                            y: [\n                                                                                0,\n                                                                                -3,\n                                                                                0\n                                                                            ]\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 0.6,\n                                                                            repeat: Infinity,\n                                                                            repeatType: \"loop\",\n                                                                            delay: 0.4\n                                                                        },\n                                                                        className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 23\n                                                    }, undefined) : isMessagePrimary(currentStep) ? formatText(pulseEmoji(getStepText(currentStep), true)) : pulseEmoji(getStepText(currentStep)),\n                                                    !typingComplete && currentStep !== 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                                                        animate: {\n                                                            opacity: [\n                                                                1,\n                                                                0\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            repeat: Infinity,\n                                                            repeatType: \"reverse\"\n                                                        },\n                                                        className: \"inline-block ml-1 w-2 h-4 bg-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: [\n                                    callHandled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                        initial: {\n                                            scale: 0.8,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            scale: 1,\n                                            opacity: 1\n                                        },\n                                        className: \"bg-green-600/30 border border-green-500/40 rounded-xl px-3 py-2 flex flex-col items-center justify-center shadow-md shadow-green-900/20 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                        initial: {\n                                                            scale: 0.5\n                                                        },\n                                                        animate: {\n                                                            scale: [\n                                                                0.5,\n                                                                1.2,\n                                                                1\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 0.5\n                                                        },\n                                                        className: \"w-5 h-5 bg-green-500 rounded-full mr-2 flex items-center justify-center text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-3 w-3\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-green-400\",\n                                                        children: \"Call Handled Successfully\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                transition: {\n                                                    delay: 0.5\n                                                },\n                                                className: \"flex items-center mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-300\",\n                                                        children: \"No Missed Opportunities\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                                                        animate: {\n                                                            rotate: [\n                                                                0,\n                                                                15,\n                                                                -15,\n                                                                0\n                                                            ],\n                                                            scale: [\n                                                                1,\n                                                                1.1,\n                                                                1\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 1.5,\n                                                            repeat: Infinity,\n                                                            repeatType: \"loop\",\n                                                            delay: 1\n                                                        },\n                                                        className: \"ml-1\",\n                                                        children: \"\\uD83D\\uDE80\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !callHandled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 flex items-center justify-center relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    className: \"absolute inset-0 rounded-full border-2 border-purple-500/50\",\n                                                    animate: {\n                                                        scale: [\n                                                            1,\n                                                            1.2,\n                                                            1\n                                                        ],\n                                                        opacity: [\n                                                            0.7,\n                                                            0,\n                                                            0.7\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 3,\n                                                        repeat: Infinity,\n                                                        ease: \"easeInOut\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    className: \"absolute inset-0 rounded-full border-2 border-purple-500/50\",\n                                                    animate: {\n                                                        scale: [\n                                                            1,\n                                                            1.5,\n                                                            1\n                                                        ],\n                                                        opacity: [\n                                                            0.5,\n                                                            0,\n                                                            0.5\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 3,\n                                                        repeat: Infinity,\n                                                        ease: \"easeInOut\",\n                                                        delay: 0.5\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    className: \"absolute inset-2 rounded-full border-2 border-purple-500/80\",\n                                                    animate: {\n                                                        rotate: 360\n                                                    },\n                                                    transition: {\n                                                        duration: 8,\n                                                        repeat: Infinity,\n                                                        ease: \"linear\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                        className: \"absolute w-2.5 h-2.5 rounded-full bg-purple-500 shadow-sm shadow-purple-900/50 top-0 left-1/2 -translate-x-1/2 -translate-y-1/2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                    className: \"w-10 h-10 rounded-full bg-purple-600/40 flex items-center justify-center shadow-inner\",\n                                                    animate: {\n                                                        boxShadow: [\n                                                            \"0 0 0px rgba(147, 51, 234, 0)\",\n                                                            \"0 0 15px rgba(147, 51, 234, 0.5)\",\n                                                            \"0 0 0px rgba(147, 51, 234, 0)\"\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 2,\n                                                        repeat: Infinity,\n                                                        ease: \"easeInOut\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"h-6 w-6 text-purple-300\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 1.5,\n                                                            d: \"M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 017.07 0m-9.9-2.83a9 9 0 0112.73 0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\CallPreviewBox.jsx\",\n        lineNumber: 234,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CallPreviewBox, \"iz792ALbluItwrVppRSoEoXatOw=\");\n_c = CallPreviewBox;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CallPreviewBox);\nvar _c;\n$RefreshReg$(_c, \"CallPreviewBox\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/CallPreviewBox.jsx\n"));

/***/ })

}]);