import './globals.css';
import { LanguageProvider } from './i18n/LanguageContext';
import GlobalBackgroundOverlay from './components/GlobalBackgroundOverlay';
import { Inter } from 'next/font/google';
import { SessionProvider } from './providers/SessionProvider';
import ConditionalNavbar from './components/ConditionalNavbar';
import ClientServiceWorkerManager from './components/ClientServiceWorkerManager';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'CallSaver - Never Miss A Customer Call Again',
  description: 'AI-Powered Call Management Platform',
  icons: {
    icon: '/favicon.svg',
    shortcut: '/favicon.svg',
    apple: '/favicon.svg',
  },
  openGraph: {
    title: 'CallSaver - Never Miss A Customer Call Again',
    description: 'AI-Powered Call Management Platform',
    type: 'website',
    images: ['/og-image.jpg'],
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#0d0d17',
};

export const revalidate = 3600;

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} bg-[#0d0d17] min-h-screen overflow-x-hidden`}>
        <GlobalBackgroundOverlay />
        <ClientServiceWorkerManager />
        <SessionProvider>
          <LanguageProvider>
            <div className="min-h-screen flex flex-col relative z-10">
              <ConditionalNavbar />
              <main className="flex-grow relative">{children}</main>
            </div>
          </LanguageProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
