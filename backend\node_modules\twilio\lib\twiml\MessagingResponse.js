"use strict";
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const TwiML_1 = __importDefault(require("./TwiML"));
class MessagingResponse extends TwiML_1.default {
    /**
     * <Response> TwiML for Messages
     */
    constructor() {
        super();
        this._propertyName = "response";
    }
    /**
     * Comments in <Response>
     *
     * @param comment - XML Comment
     */
    comment(comment) {
        return this.response.comment(comment);
    }
    /**
     * Comments after <Response>
     *
     * @param comment - XML Comment
     */
    commentAfter(comment) {
        return this.response.commentAfter(comment);
    }
    /**
     * Comments before <Response>
     *
     * @param comment - XML Comment
     */
    commentBefore(comment) {
        return this.response.commentBefore(comment);
    }
    message(attributes, body) {
        if (typeof attributes === "string") {
            body = attributes;
            attributes = {};
        }
        return new MessagingResponse.Message(this.response.ele("Message", attributes, body));
    }
    redirect(attributes, url) {
        if (typeof attributes === "string") {
            url = attributes;
            attributes = {};
        }
        return new MessagingResponse.Redirect(this.response.ele("Redirect", attributes, url));
    }
}
(function (MessagingResponse) {
    class Body extends TwiML_1.default {
        /**
         * <Body> TwiML Noun
         */
        constructor(body) {
            super();
            this.body = body;
            this._propertyName = "body";
        }
    }
    MessagingResponse.Body = Body;
    class Media extends TwiML_1.default {
        /**
         * <Media> TwiML Noun
         */
        constructor(media) {
            super();
            this.media = media;
            this._propertyName = "media";
        }
    }
    MessagingResponse.Media = Media;
    class Message extends TwiML_1.default {
        /**
         * <Message> TwiML Verb
         */
        constructor(message) {
            super();
            this.message = message;
            this._propertyName = "message";
        }
        body(attributes, message) {
            if (typeof attributes === "string") {
                message = attributes;
                attributes = {};
            }
            return new MessagingResponse.Body(this.message.ele("Body", attributes, message));
        }
        media(attributes, url) {
            if (typeof attributes === "string") {
                url = attributes;
                attributes = {};
            }
            return new MessagingResponse.Media(this.message.ele("Media", attributes, url));
        }
    }
    MessagingResponse.Message = Message;
    class Redirect extends TwiML_1.default {
        /**
         * <Redirect> TwiML Verb
         */
        constructor(redirect) {
            super();
            this.redirect = redirect;
            this._propertyName = "redirect";
        }
    }
    MessagingResponse.Redirect = Redirect;
})(MessagingResponse || (MessagingResponse = {}));
module.exports = MessagingResponse;
