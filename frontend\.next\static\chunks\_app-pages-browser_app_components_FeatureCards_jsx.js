"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_FeatureCards_jsx"],{

/***/ "(app-pages-browser)/./app/components/FeatureCards.jsx":
/*!*****************************************!*\
  !*** ./app/components/FeatureCards.jsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FeatureCards; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../i18n/LanguageContext */ \"(app-pages-browser)/./app/i18n/LanguageContext.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction FeatureCards() {\n    _s();\n    const { isRTL } = (0,_i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useInView)(ref, {\n        once: true,\n        amount: 0.2\n    });\n    const [particleProps, setParticleProps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Memoize the features array to prevent it from recreating on every render\n    const features = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                id: 1,\n                title: \"Smart Call Routing\",\n                description: \"Directs calls based on priority and availability, ensuring important calls are never missed and are directed to the right person.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-7 w-7\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                highlights: [\n                    \"Intelligent call prioritization\",\n                    \"Customizable routing rules\",\n                    \"Automatic call distribution\"\n                ],\n                accentClass: \"bg-gradient-to-r from-indigo-500 to-blue-500\",\n                iconBgClass: \"bg-gradient-to-br from-indigo-500/60 to-blue-500/60\",\n                checkColor: \"text-white\",\n                laserColor: \"laser-particle-blue\",\n                bgClass: \"bg-gradient-to-br from-indigo-600/50 to-blue-800/50 border-indigo-400/30\",\n                titleColor: \"text-white\",\n                descriptionColor: \"text-blue-100\"\n            },\n            {\n                id: 2,\n                title: \"AI Call Summaries\",\n                description: \"Get the key points without listening to recordings. Our AI generates concise, accurate summaries of every conversation.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-7 w-7\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                highlights: [\n                    \"Automated call summaries\",\n                    \"Action item extraction\",\n                    \"Follow-up reminders\"\n                ],\n                accentClass: \"bg-gradient-to-r from-purple-500 to-indigo-500\",\n                iconBgClass: \"bg-gradient-to-br from-purple-500/60 to-indigo-500/60\",\n                checkColor: \"text-white\",\n                laserColor: \"laser-particle-purple\",\n                bgClass: \"bg-gradient-to-br from-purple-600/50 to-indigo-800/50 border-purple-400/30\",\n                titleColor: \"text-white\",\n                descriptionColor: \"text-purple-100\"\n            },\n            {\n                id: 3,\n                title: \"Voice & Text Transcription\",\n                description: \"Every call documented and searchable with high-accuracy transcription that captures even nuanced conversations.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-7 w-7\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                highlights: [\n                    \"Calendar synchronization\",\n                    \"CRM data capture\",\n                    \"Custom workflow automation\"\n                ],\n                accentClass: \"bg-gradient-to-r from-green-500 to-emerald-500\",\n                iconBgClass: \"bg-gradient-to-br from-green-500/60 to-emerald-500/60\",\n                checkColor: \"text-white\",\n                laserColor: \"laser-particle-green\",\n                bgClass: \"bg-gradient-to-br from-green-600/50 to-emerald-800/50 border-green-400/30\",\n                titleColor: \"text-white\",\n                descriptionColor: \"text-green-100\"\n            },\n            {\n                id: 4,\n                title: \"24/7 Revenue Protection\",\n                description: \"Never miss another sales opportunity. Our system works around the clock to ensure every potential customer is engaged and converted.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    className: \"h-7 w-7\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 1.5,\n                        d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                highlights: [\n                    \"Always-on conversion\",\n                    \"After-hours sales capture\",\n                    \"Zero missed opportunities\"\n                ],\n                accentClass: \"bg-gradient-to-r from-pink-500 to-rose-500\",\n                iconBgClass: \"bg-gradient-to-br from-pink-500/60 to-rose-500/60\",\n                checkColor: \"text-white\",\n                laserColor: \"laser-particle-pink\",\n                bgClass: \"bg-gradient-to-br from-pink-600/50 to-rose-800/50 border-pink-400/30\",\n                titleColor: \"text-white\",\n                descriptionColor: \"text-pink-100\"\n            }\n        ], []);\n    // Generate random particle properties on client-side only\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const newParticleProps = [];\n        features.forEach((feature)=>{\n            const featureParticles = [];\n            for(let i = 0; i < 3; i++){\n                featureParticles.push({\n                    width: Math.random() * 4 + 2,\n                    height: Math.random() * 4 + 2,\n                    left: Math.random() * 80 + 10,\n                    top: Math.random() * 80 + 10,\n                    xMove: Math.random() * 40 - 20,\n                    yMove: Math.random() * 40 - 20,\n                    duration: Math.random() * 3 + 2\n                });\n            }\n            newParticleProps.push(featureParticles);\n        });\n        setParticleProps(newParticleProps);\n    }, [\n        features\n    ]);\n    // Animation variants\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const headingVariants = {\n        hidden: {\n            opacity: 0,\n            y: -20\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    const cardVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: (i)=>({\n                opacity: 1,\n                y: 0,\n                transition: {\n                    delay: i * 0.1,\n                    duration: 0.5,\n                    ease: \"easeOut\"\n                }\n            }),\n        hover: {\n            y: -8,\n            scale: 1.03,\n            boxShadow: \"0 10px 25px rgba(80, 60, 200, 0.2)\",\n            transition: {\n                type: \"spring\",\n                stiffness: 400,\n                damping: 10\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        ref: ref,\n        className: \"py-10 relative overflow-hidden neon-bg-glow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-1/4 w-1/3 h-1/3 bg-purple-900/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-blue-900/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container max-w-7xl mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"text-center mb-12\",\n                        initial: \"hidden\",\n                        animate: isInView ? \"visible\" : \"hidden\",\n                        variants: headingVariants,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"heading-lg laser-gradient-text mb-4\",\n                                \"data-text\": \"Revolutionary AI Voice & Text\",\n                                children: \"Revolutionary AI Voice & Text\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"subheading-text max-w-3xl mx-auto\",\n                                children: [\n                                    \"Transform how your business handles communication with AI that actually \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-purple-400 font-medium\",\n                                        children: \"talks and listens\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 85\n                                    }, this),\n                                    \" to your customers—not just responds to texts. Save time, increase revenue, and deliver exceptional service 24/7.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 features-grid\",\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: isInView ? \"visible\" : \"hidden\",\n                        children: features.map((feature, index)=>{\n                            var _particleProps_index;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                custom: index,\n                                variants: cardVariants,\n                                whileHover: \"hover\",\n                                className: \"laser-card p-6 rounded-xl relative group min-h-[320px] flex flex-col \".concat(feature.bgClass, \" border border-opacity-30 shadow-[0_0_15px_rgba(80,60,200,0.15)] backdrop-blur-md\"),\n                                role: \"article\",\n                                \"aria-labelledby\": \"feature-title-\".concat(feature.id),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                        className: \"absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                        animate: {\n                                            boxShadow: [\n                                                \"0 0 0px rgba(128, 0, 255, 0)\",\n                                                \"0 0 15px rgba(128, 0, 255, 0.3)\",\n                                                \"0 0 0px rgba(128, 0, 255, 0)\"\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            repeat: Infinity,\n                                            ease: \"easeInOut\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 overflow-hidden rounded-xl pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                        children: (_particleProps_index = particleProps[index]) === null || _particleProps_index === void 0 ? void 0 : _particleProps_index.map((particle, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute rounded-full \".concat(feature.laserColor),\n                                                style: {\n                                                    width: \"\".concat(particle.width, \"px\"),\n                                                    height: \"\".concat(particle.height, \"px\"),\n                                                    left: \"\".concat(particle.left, \"%\"),\n                                                    top: \"\".concat(particle.top, \"%\"),\n                                                    opacity: 0.7,\n                                                    filter: \"blur(2px)\"\n                                                },\n                                                animate: {\n                                                    x: [\n                                                        0,\n                                                        particle.xMove\n                                                    ],\n                                                    y: [\n                                                        0,\n                                                        particle.yMove\n                                                    ],\n                                                    opacity: [\n                                                        0.7,\n                                                        0.9,\n                                                        0.7\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: particle.duration,\n                                                    repeat: Infinity,\n                                                    repeatType: \"reverse\",\n                                                    ease: \"easeInOut\"\n                                                }\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg \".concat(feature.iconBgClass, \" mr-3 laser-icon\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                    className: \"text-white\",\n                                                    whileHover: {\n                                                        scale: 1.1,\n                                                        rotate: 5\n                                                    },\n                                                    transition: {\n                                                        type: \"spring\",\n                                                        stiffness: 300,\n                                                        damping: 10\n                                                    },\n                                                    children: feature.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                id: \"feature-title-\".concat(feature.id),\n                                                className: \"text-xl font-bold \".concat(feature.titleColor, \" group-hover:text-white transition-colors duration-300 pt-1\"),\n                                                children: feature.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base \".concat(feature.descriptionColor, \" mb-5 leading-relaxed\"),\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-2.5 mt-auto\",\n                                        children: feature.highlights.map((highlight, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center text-sm feature-highlight py-1.5 px-2.5 rounded-md bg-white/5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                                                        className: \"mr-2.5 \".concat(feature.checkColor, \" flex-shrink-0\"),\n                                                        whileHover: {\n                                                            scale: 1.2,\n                                                            rotate: 5\n                                                        },\n                                                        style: {\n                                                            animationDelay: \"\".concat(i * 0.5, \"s\")\n                                                        },\n                                                        \"aria-hidden\": \"true\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: highlight\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-1 w-16 mt-4 rounded-full \".concat(feature.accentClass, \" relative overflow-hidden\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute inset-0\",\n                                                animate: {\n                                                    boxShadow: [\n                                                        \"0 0 0px rgba(128, 0, 255, 0)\",\n                                                        \"0 0 10px rgba(128, 0, 255, 0.5)\",\n                                                        \"0 0 0px rgba(128, 0, 255, 0)\"\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity,\n                                                    ease: \"easeInOut\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute inset-0 bg-white/10\",\n                                                animate: {\n                                                    x: [\n                                                        \"-100%\",\n                                                        \"100%\"\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 1.5,\n                                                    repeat: Infinity,\n                                                    ease: \"easeInOut\",\n                                                    repeatDelay: 1\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"laser-divider mt-10 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\FeatureCards.jsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n_s(FeatureCards, \"eeGy6uDGGiqeAh3cZFKV0xcZRtM=\", false, function() {\n    return [\n        _i18n_LanguageContext__WEBPACK_IMPORTED_MODULE_2__.useLanguage,\n        framer_motion__WEBPACK_IMPORTED_MODULE_3__.useInView\n    ];\n});\n_c = FeatureCards;\nvar _c;\n$RefreshReg$(_c, \"FeatureCards\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/FeatureCards.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveElements: function() { return /* binding */ resolveElements; }\n/* harmony export */ });\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n\n\nfunction resolveElements(elements, scope, selectorCache) {\n    var _a;\n    if (typeof elements === \"string\") {\n        let root = document;\n        if (scope) {\n            (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant)(Boolean(scope.current), \"Scope provided, but no element detected.\");\n            root = scope.current;\n        }\n        if (selectorCache) {\n            (_a = selectorCache[elements]) !== null && _a !== void 0 ? _a : (selectorCache[elements] = root.querySelectorAll(elements));\n            elements = selectorCache[elements];\n        }\n        else {\n            elements = root.querySelectorAll(elements);\n        }\n    }\n    else if (elements instanceof Element) {\n        elements = [elements];\n    }\n    /**\n     * Return an empty array\n     */\n    return Array.from(elements || []);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9yZXNvbHZlLWVsZW1lbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNEOztBQUV0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSw0REFBUztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9yZXNvbHZlLWVsZW1lbnQubWpzPzE0OTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW52YXJpYW50IH0gZnJvbSAnLi4vLi4vLi4vdXRpbHMvZXJyb3JzLm1qcyc7XG5cbmZ1bmN0aW9uIHJlc29sdmVFbGVtZW50cyhlbGVtZW50cywgc2NvcGUsIHNlbGVjdG9yQ2FjaGUpIHtcbiAgICB2YXIgX2E7XG4gICAgaWYgKHR5cGVvZiBlbGVtZW50cyA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICBsZXQgcm9vdCA9IGRvY3VtZW50O1xuICAgICAgICBpZiAoc2NvcGUpIHtcbiAgICAgICAgICAgIGludmFyaWFudChCb29sZWFuKHNjb3BlLmN1cnJlbnQpLCBcIlNjb3BlIHByb3ZpZGVkLCBidXQgbm8gZWxlbWVudCBkZXRlY3RlZC5cIik7XG4gICAgICAgICAgICByb290ID0gc2NvcGUuY3VycmVudDtcbiAgICAgICAgfVxuICAgICAgICBpZiAoc2VsZWN0b3JDYWNoZSkge1xuICAgICAgICAgICAgKF9hID0gc2VsZWN0b3JDYWNoZVtlbGVtZW50c10pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IChzZWxlY3RvckNhY2hlW2VsZW1lbnRzXSA9IHJvb3QucXVlcnlTZWxlY3RvckFsbChlbGVtZW50cykpO1xuICAgICAgICAgICAgZWxlbWVudHMgPSBzZWxlY3RvckNhY2hlW2VsZW1lbnRzXTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGVsZW1lbnRzID0gcm9vdC5xdWVyeVNlbGVjdG9yQWxsKGVsZW1lbnRzKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIGlmIChlbGVtZW50cyBpbnN0YW5jZW9mIEVsZW1lbnQpIHtcbiAgICAgICAgZWxlbWVudHMgPSBbZWxlbWVudHNdO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZXR1cm4gYW4gZW1wdHkgYXJyYXlcbiAgICAgKi9cbiAgICByZXR1cm4gQXJyYXkuZnJvbShlbGVtZW50cyB8fCBbXSk7XG59XG5cbmV4cG9ydCB7IHJlc29sdmVFbGVtZW50cyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inView: function() { return /* binding */ inView; }\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_element_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/resolve-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/resolve-element.mjs\");\n\n\nconst thresholds = {\n    some: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"some\" } = {}) {\n    const elements = (0,_utils_resolve_element_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry);\n                if (typeof newOnEnd === \"function\") {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (onEnd) {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-in-view.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInView: function() { return /* binding */ useInView; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../render/dom/viewport/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\");\n\n\n\nfunction useInView(ref, { root, margin, amount, once = false } = {}) {\n    const [isInView, setInView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!ref.current || (once && isInView))\n            return;\n        const onEnter = () => {\n            setInView(true);\n            return once ? undefined : () => setInView(false);\n        };\n        const options = {\n            root: (root && root.current) || undefined,\n            margin,\n            amount,\n        };\n        return (0,_render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__.inView)(ref.current, onEnter, options);\n    }, [root, ref, margin, once, amount]);\n    return isInView;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWluLXZpZXcubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNjOztBQUUxRCwwQkFBMEIscUNBQXFDLElBQUk7QUFDbkUsa0NBQWtDLCtDQUFRO0FBQzFDLElBQUksZ0RBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzRUFBTTtBQUNyQixLQUFLO0FBQ0w7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy91c2UtaW4tdmlldy5tanM/YzhmMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgaW5WaWV3IH0gZnJvbSAnLi4vcmVuZGVyL2RvbS92aWV3cG9ydC9pbmRleC5tanMnO1xuXG5mdW5jdGlvbiB1c2VJblZpZXcocmVmLCB7IHJvb3QsIG1hcmdpbiwgYW1vdW50LCBvbmNlID0gZmFsc2UgfSA9IHt9KSB7XG4gICAgY29uc3QgW2lzSW5WaWV3LCBzZXRJblZpZXddID0gdXNlU3RhdGUoZmFsc2UpO1xuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmICghcmVmLmN1cnJlbnQgfHwgKG9uY2UgJiYgaXNJblZpZXcpKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBjb25zdCBvbkVudGVyID0gKCkgPT4ge1xuICAgICAgICAgICAgc2V0SW5WaWV3KHRydWUpO1xuICAgICAgICAgICAgcmV0dXJuIG9uY2UgPyB1bmRlZmluZWQgOiAoKSA9PiBzZXRJblZpZXcoZmFsc2UpO1xuICAgICAgICB9O1xuICAgICAgICBjb25zdCBvcHRpb25zID0ge1xuICAgICAgICAgICAgcm9vdDogKHJvb3QgJiYgcm9vdC5jdXJyZW50KSB8fCB1bmRlZmluZWQsXG4gICAgICAgICAgICBtYXJnaW4sXG4gICAgICAgICAgICBhbW91bnQsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiBpblZpZXcocmVmLmN1cnJlbnQsIG9uRW50ZXIsIG9wdGlvbnMpO1xuICAgIH0sIFtyb290LCByZWYsIG1hcmdpbiwgb25jZSwgYW1vdW50XSk7XG4gICAgcmV0dXJuIGlzSW5WaWV3O1xufVxuXG5leHBvcnQgeyB1c2VJblZpZXcgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\n"));

/***/ })

}]);