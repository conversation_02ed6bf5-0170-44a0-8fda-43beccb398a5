/// <reference types="node" />
import { inspect, InspectOptions } from "util";
import V1 from "../V1";
/**
 * The status of the Flex onboarding. Can be: `ok`, `inprogress`,`notstarted`.
 */
export type ConfigurationStatus = "ok" | "inprogress" | "notstarted";
/**
 * Options to pass to fetch a ConfigurationInstance
 */
export interface ConfigurationContextFetchOptions {
    /** The Pinned UI version of the Configuration resource to fetch. */
    uiVersion?: string;
}
/**
 * Options to pass to update a ConfigurationInstance
 */
export interface ConfigurationContextUpdateOptions {
    /**  */
    body?: object;
}
export interface ConfigurationContext {
    /**
     * Fetch a ConfigurationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConfigurationInstance
     */
    fetch(callback?: (error: Error | null, item?: ConfigurationInstance) => any): Promise<ConfigurationInstance>;
    /**
     * Fetch a ConfigurationInstance
     *
     * @param params - Parameter for request
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConfigurationInstance
     */
    fetch(params: ConfigurationContextFetchOptions, callback?: (error: Error | null, item?: ConfigurationInstance) => any): Promise<ConfigurationInstance>;
    /**
     * Update a ConfigurationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConfigurationInstance
     */
    update(callback?: (error: Error | null, item?: ConfigurationInstance) => any): Promise<ConfigurationInstance>;
    /**
     * Update a ConfigurationInstance
     *
     * @param params - Body for request
     * @param headers - header params for request
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConfigurationInstance
     */
    update(params: object, headers?: any, callback?: (error: Error | null, item?: ConfigurationInstance) => any): Promise<ConfigurationInstance>;
    /**
     * Provide a user-friendly representation
     */
    toJSON(): any;
    [inspect.custom](_depth: any, options: InspectOptions): any;
}
export interface ConfigurationContextSolution {
}
export declare class ConfigurationContextImpl implements ConfigurationContext {
    protected _version: V1;
    protected _solution: ConfigurationContextSolution;
    protected _uri: string;
    constructor(_version: V1);
    fetch(params?: ConfigurationContextFetchOptions | ((error: Error | null, item?: ConfigurationInstance) => any), callback?: (error: Error | null, item?: ConfigurationInstance) => any): Promise<ConfigurationInstance>;
    update(params?: object | ((error: Error | null, item?: ConfigurationInstance) => any), headers?: any, callback?: (error: Error | null, item?: ConfigurationInstance) => any): Promise<ConfigurationInstance>;
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON(): ConfigurationContextSolution;
    [inspect.custom](_depth: any, options: InspectOptions): string;
}
interface ConfigurationResource {
    account_sid: string;
    date_created: Date;
    date_updated: Date;
    attributes: Record<string, object>;
    status: ConfigurationStatus;
    taskrouter_workspace_sid: string;
    taskrouter_target_workflow_sid: string;
    taskrouter_target_taskqueue_sid: string;
    taskrouter_taskqueues: Array<Record<string, object>>;
    taskrouter_skills: Array<Record<string, object>>;
    taskrouter_worker_channels: Record<string, object>;
    taskrouter_worker_attributes: Record<string, object>;
    taskrouter_offline_activity_sid: string;
    runtime_domain: string;
    messaging_service_instance_sid: string;
    chat_service_instance_sid: string;
    flex_service_instance_sid: string;
    flex_instance_sid: string;
    ui_language: string;
    ui_attributes: Record<string, object>;
    ui_dependencies: Record<string, object>;
    ui_version: string;
    service_version: string;
    call_recording_enabled: boolean;
    call_recording_webhook_url: string;
    crm_enabled: boolean;
    crm_type: string;
    crm_callback_url: string;
    crm_fallback_url: string;
    crm_attributes: Record<string, object>;
    public_attributes: Record<string, object>;
    plugin_service_enabled: boolean;
    plugin_service_attributes: Record<string, object>;
    integrations: Array<Record<string, object>>;
    outbound_call_flows: Record<string, object>;
    serverless_service_sids: Array<string>;
    queue_stats_configuration: Record<string, object>;
    notifications: Record<string, object>;
    markdown: Record<string, object>;
    url: string;
    flex_insights_hr: Record<string, object>;
    flex_insights_drilldown: boolean;
    flex_url: string;
    channel_configs: Array<Record<string, object>>;
    debugger_integration: Record<string, object>;
    flex_ui_status_report: Record<string, object>;
    agent_conv_end_methods: Record<string, object>;
    citrix_voice_vdi: Record<string, object>;
    offline_config: Record<string, object>;
}
export declare class ConfigurationInstance {
    protected _version: V1;
    protected _solution: ConfigurationContextSolution;
    protected _context?: ConfigurationContext;
    constructor(_version: V1, payload: ConfigurationResource);
    /**
     * The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Configuration resource.
     */
    accountSid: string;
    /**
     * The date and time in GMT when the Configuration resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
     */
    dateCreated: Date;
    /**
     * The date and time in GMT when the Configuration resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
     */
    dateUpdated: Date;
    /**
     * An object that contains application-specific data.
     */
    attributes: Record<string, object>;
    status: ConfigurationStatus;
    /**
     * The SID of the TaskRouter Workspace.
     */
    taskrouterWorkspaceSid: string;
    /**
     * The SID of the TaskRouter target Workflow.
     */
    taskrouterTargetWorkflowSid: string;
    /**
     * The SID of the TaskRouter Target TaskQueue.
     */
    taskrouterTargetTaskqueueSid: string;
    /**
     * The list of TaskRouter TaskQueues.
     */
    taskrouterTaskqueues: Array<Record<string, object>>;
    /**
     * The Skill description for TaskRouter workers.
     */
    taskrouterSkills: Array<Record<string, object>>;
    /**
     * The TaskRouter default channel capacities and availability for workers.
     */
    taskrouterWorkerChannels: Record<string, object>;
    /**
     * The TaskRouter Worker attributes.
     */
    taskrouterWorkerAttributes: Record<string, object>;
    /**
     * The TaskRouter SID of the offline activity.
     */
    taskrouterOfflineActivitySid: string;
    /**
     * The URL where the Flex instance is hosted.
     */
    runtimeDomain: string;
    /**
     * The SID of the Messaging service instance.
     */
    messagingServiceInstanceSid: string;
    /**
     * The SID of the chat service this user belongs to.
     */
    chatServiceInstanceSid: string;
    /**
     * The SID of the Flex service instance.
     */
    flexServiceInstanceSid: string;
    /**
     * The SID of the Flex instance.
     */
    flexInstanceSid: string;
    /**
     * The primary language of the Flex UI.
     */
    uiLanguage: string;
    /**
     * The object that describes Flex UI characteristics and settings.
     */
    uiAttributes: Record<string, object>;
    /**
     * The object that defines the NPM packages and versions to be used in Hosted Flex.
     */
    uiDependencies: Record<string, object>;
    /**
     * The Pinned UI version.
     */
    uiVersion: string;
    /**
     * The Flex Service version.
     */
    serviceVersion: string;
    /**
     * Whether call recording is enabled.
     */
    callRecordingEnabled: boolean;
    /**
     * The call recording webhook URL.
     */
    callRecordingWebhookUrl: string;
    /**
     * Whether CRM is present for Flex.
     */
    crmEnabled: boolean;
    /**
     * The CRM type.
     */
    crmType: string;
    /**
     * The CRM Callback URL.
     */
    crmCallbackUrl: string;
    /**
     * The CRM Fallback URL.
     */
    crmFallbackUrl: string;
    /**
     * An object that contains the CRM attributes.
     */
    crmAttributes: Record<string, object>;
    /**
     * The list of public attributes, which are visible to unauthenticated clients.
     */
    publicAttributes: Record<string, object>;
    /**
     * Whether the plugin service enabled.
     */
    pluginServiceEnabled: boolean;
    /**
     * The plugin service attributes.
     */
    pluginServiceAttributes: Record<string, object>;
    /**
     * A list of objects that contain the configurations for the Integrations supported in this configuration.
     */
    integrations: Array<Record<string, object>>;
    /**
     * The list of outbound call flows.
     */
    outboundCallFlows: Record<string, object>;
    /**
     * The list of serverless service SIDs.
     */
    serverlessServiceSids: Array<string>;
    /**
     * Configurable parameters for Queues Statistics.
     */
    queueStatsConfiguration: Record<string, object>;
    /**
     * Configurable parameters for Notifications.
     */
    notifications: Record<string, object>;
    /**
     * Configurable parameters for Markdown.
     */
    markdown: Record<string, object>;
    /**
     * The absolute URL of the Configuration resource.
     */
    url: string;
    /**
     * Object with enabled/disabled flag with list of workspaces.
     */
    flexInsightsHr: Record<string, object>;
    /**
     * Setting this to true will redirect Flex UI to the URL set in flex_url
     */
    flexInsightsDrilldown: boolean;
    /**
     * URL to redirect to in case drilldown is enabled.
     */
    flexUrl: string;
    /**
     * Settings for different limits for Flex Conversations channels attachments.
     */
    channelConfigs: Array<Record<string, object>>;
    /**
     * Configurable parameters for Debugger Integration.
     */
    debuggerIntegration: Record<string, object>;
    /**
     * Configurable parameters for Flex UI Status report.
     */
    flexUiStatusReport: Record<string, object>;
    /**
     * Agent conversation end methods.
     */
    agentConvEndMethods: Record<string, object>;
    /**
     * Citrix voice vdi configuration and settings.
     */
    citrixVoiceVdi: Record<string, object>;
    /**
     * Presence and presence ttl configuration
     */
    offlineConfig: Record<string, object>;
    private get _proxy();
    /**
     * Fetch a ConfigurationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConfigurationInstance
     */
    fetch(callback?: (error: Error | null, item?: ConfigurationInstance) => any): Promise<ConfigurationInstance>;
    /**
     * Fetch a ConfigurationInstance
     *
     * @param params - Parameter for request
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConfigurationInstance
     */
    fetch(params: ConfigurationContextFetchOptions, callback?: (error: Error | null, item?: ConfigurationInstance) => any): Promise<ConfigurationInstance>;
    /**
     * Update a ConfigurationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConfigurationInstance
     */
    update(callback?: (error: Error | null, item?: ConfigurationInstance) => any): Promise<ConfigurationInstance>;
    /**
     * Update a ConfigurationInstance
     *
     * @param params - Body for request
     * @param headers - header params for request
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConfigurationInstance
     */
    update(params: object, headers?: any, callback?: (error: Error | null, item?: ConfigurationInstance) => any): Promise<ConfigurationInstance>;
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON(): {
        accountSid: string;
        dateCreated: Date;
        dateUpdated: Date;
        attributes: Record<string, object>;
        status: ConfigurationStatus;
        taskrouterWorkspaceSid: string;
        taskrouterTargetWorkflowSid: string;
        taskrouterTargetTaskqueueSid: string;
        taskrouterTaskqueues: Record<string, object>[];
        taskrouterSkills: Record<string, object>[];
        taskrouterWorkerChannels: Record<string, object>;
        taskrouterWorkerAttributes: Record<string, object>;
        taskrouterOfflineActivitySid: string;
        runtimeDomain: string;
        messagingServiceInstanceSid: string;
        chatServiceInstanceSid: string;
        flexServiceInstanceSid: string;
        flexInstanceSid: string;
        uiLanguage: string;
        uiAttributes: Record<string, object>;
        uiDependencies: Record<string, object>;
        uiVersion: string;
        serviceVersion: string;
        callRecordingEnabled: boolean;
        callRecordingWebhookUrl: string;
        crmEnabled: boolean;
        crmType: string;
        crmCallbackUrl: string;
        crmFallbackUrl: string;
        crmAttributes: Record<string, object>;
        publicAttributes: Record<string, object>;
        pluginServiceEnabled: boolean;
        pluginServiceAttributes: Record<string, object>;
        integrations: Record<string, object>[];
        outboundCallFlows: Record<string, object>;
        serverlessServiceSids: string[];
        queueStatsConfiguration: Record<string, object>;
        notifications: Record<string, object>;
        markdown: Record<string, object>;
        url: string;
        flexInsightsHr: Record<string, object>;
        flexInsightsDrilldown: boolean;
        flexUrl: string;
        channelConfigs: Record<string, object>[];
        debuggerIntegration: Record<string, object>;
        flexUiStatusReport: Record<string, object>;
        agentConvEndMethods: Record<string, object>;
        citrixVoiceVdi: Record<string, object>;
        offlineConfig: Record<string, object>;
    };
    [inspect.custom](_depth: any, options: InspectOptions): string;
}
export interface ConfigurationSolution {
}
export interface ConfigurationListInstance {
    _version: V1;
    _solution: ConfigurationSolution;
    _uri: string;
    (): ConfigurationContext;
    get(): ConfigurationContext;
    /**
     * Provide a user-friendly representation
     */
    toJSON(): any;
    [inspect.custom](_depth: any, options: InspectOptions): any;
}
export declare function ConfigurationListInstance(version: V1): ConfigurationListInstance;
export {};
