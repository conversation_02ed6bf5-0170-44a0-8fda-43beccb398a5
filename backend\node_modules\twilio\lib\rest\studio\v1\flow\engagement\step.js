"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Studio
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StepPage = exports.StepListInstance = exports.StepInstance = exports.StepContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../base/Page"));
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
const stepContext_1 = require("./step/stepContext");
class StepContextImpl {
    constructor(_version, flowSid, engagementSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(flowSid)) {
            throw new Error("Parameter 'flowSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(engagementSid)) {
            throw new Error("Parameter 'engagementSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { flowSid, engagementSid, sid };
        this._uri = `/Flows/${flowSid}/Engagements/${engagementSid}/Steps/${sid}`;
    }
    get stepContext() {
        this._stepContext =
            this._stepContext ||
                (0, stepContext_1.StepContextListInstance)(this._version, this._solution.flowSid, this._solution.engagementSid, this._solution.sid);
        return this._stepContext;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new StepInstance(operationVersion, payload, instance._solution.flowSid, instance._solution.engagementSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.StepContextImpl = StepContextImpl;
class StepInstance {
    constructor(_version, payload, flowSid, engagementSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.flowSid = payload.flow_sid;
        this.engagementSid = payload.engagement_sid;
        this.name = payload.name;
        this.context = payload.context;
        this.transitionedFrom = payload.transitioned_from;
        this.transitionedTo = payload.transitioned_to;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { flowSid, engagementSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new StepContextImpl(this._version, this._solution.flowSid, this._solution.engagementSid, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a StepInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed StepInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Access the stepContext.
     */
    stepContext() {
        return this._proxy.stepContext;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            flowSid: this.flowSid,
            engagementSid: this.engagementSid,
            name: this.name,
            context: this.context,
            transitionedFrom: this.transitionedFrom,
            transitionedTo: this.transitionedTo,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.StepInstance = StepInstance;
function StepListInstance(version, flowSid, engagementSid) {
    if (!(0, utility_1.isValidPathParam)(flowSid)) {
        throw new Error("Parameter 'flowSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(engagementSid)) {
        throw new Error("Parameter 'engagementSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new StepContextImpl(version, flowSid, engagementSid, sid);
    };
    instance._version = version;
    instance._solution = { flowSid, engagementSid };
    instance._uri = `/Flows/${flowSid}/Engagements/${engagementSid}/Steps`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new StepPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new StepPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.StepListInstance = StepListInstance;
class StepPage extends Page_1.default {
    /**
     * Initialize the StepPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of StepInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new StepInstance(this._version, payload, this._solution.flowSid, this._solution.engagementSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.StepPage = StepPage;
