// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id            String         @id @default(cuid())
  email         String         @unique
  name          String?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  phoneNumbers  PhoneNumber[]
  calls         Call[]
  messages      Message[]
  automations   Automation[]
  subscriptions Subscription[]
}

model PhoneNumber {
  id           String    @id @default(cuid())
  number       String    @unique
  friendlyName String?
  countryCode  String
  region       String?
  locality     String?
  isActive     Boolean   @default(true)
  isPrimary    Boolean   @default(false)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  userId       String
  user         User      @relation(fields: [userId], references: [id])
  calls        Call[]
  messages     Message[]
  capabilities Json? // Store capabilities as JSON: { voice: true, sms: true, fax: false }
}

model Call {
  id            String       @id @default(cuid())
  sid           String?      @unique // Twilio call SID
  from          String
  to            String
  status        String
  duration      Int?
  price         Float?
  direction     String // inbound or outbound
  answeredBy    String?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  userId        String
  user          User         @relation(fields: [userId], references: [id])
  phoneNumberId String?
  phoneNumber   PhoneNumber? @relation(fields: [phoneNumberId], references: [id])
  recordingUrl  String?
  transcription String?
  metadata      Json? // Additional call metadata
}

model Message {
  id            String       @id @default(cuid())
  sid           String?      @unique // Twilio message SID
  from          String
  to            String
  body          String
  status        String
  direction     String // inbound or outbound
  price         Float?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  userId        String
  user          User         @relation(fields: [userId], references: [id])
  phoneNumberId String?
  phoneNumber   PhoneNumber? @relation(fields: [phoneNumberId], references: [id])
  mediaUrls     String[] // Array of media URLs
  metadata      Json? // Additional message metadata
}

model Automation {
  id        String   @id @default(cuid())
  name      String
  type      String // call, sms, etc.
  trigger   String // event that triggers the automation
  action    Json // Action to take when triggered
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String
  user      User     @relation(fields: [userId], references: [id])
}

model Subscription {
  id                   String    @id @default(cuid())
  stripeCustomerId     String?
  stripeSubscriptionId String?
  stripePriceId        String?
  status               String // active, canceled, etc.
  plan                 String // starter, pro, enterprise
  interval             String // monthly, annual
  currentPeriodStart   DateTime?
  currentPeriodEnd     DateTime?
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  userId               String
  user                 User      @relation(fields: [userId], references: [id])
}
