/// <reference types="node" />
import { inspect, InspectOptions } from "util";
import V2 from "../../../../V2";
/**
 * Options to pass to create a NotificationInstance
 */
export interface NotificationListInstanceCreateOptions {
    /** How long, in seconds, the notification is valid. Can be an integer between 0 and 300. Default is 300. Delivery is attempted until the TTL elapses, even if the device is offline. 0 means that the notification delivery is attempted immediately, only once, and is not stored for future delivery. */
    ttl?: number;
}
export interface NotificationSolution {
    serviceSid: string;
    identity: string;
    challengeSid: string;
}
export interface NotificationListInstance {
    _version: V2;
    _solution: NotificationSolution;
    _uri: string;
    /**
     * Create a NotificationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed NotificationInstance
     */
    create(callback?: (error: Error | null, item?: NotificationInstance) => any): Promise<NotificationInstance>;
    /**
     * Create a NotificationInstance
     *
     * @param params - Parameter for request
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed NotificationInstance
     */
    create(params: NotificationListInstanceCreateOptions, callback?: (error: Error | null, item?: NotificationInstance) => any): Promise<NotificationInstance>;
    /**
     * Provide a user-friendly representation
     */
    toJSON(): any;
    [inspect.custom](_depth: any, options: InspectOptions): any;
}
export declare function NotificationListInstance(version: V2, serviceSid: string, identity: string, challengeSid: string): NotificationListInstance;
interface NotificationResource {
    sid: string;
    account_sid: string;
    service_sid: string;
    entity_sid: string;
    identity: string;
    challenge_sid: string;
    priority: string;
    ttl: number;
    date_created: Date;
}
export declare class NotificationInstance {
    protected _version: V2;
    constructor(_version: V2, payload: NotificationResource, serviceSid: string, identity: string, challengeSid: string);
    /**
     * A 34 character string that uniquely identifies this Notification.
     */
    sid: string;
    /**
     * The unique SID identifier of the Account.
     */
    accountSid: string;
    /**
     * The unique SID identifier of the Service.
     */
    serviceSid: string;
    /**
     * The unique SID identifier of the Entity.
     */
    entitySid: string;
    /**
     * Customer unique identity for the Entity owner of the Challenge. This identifier should be immutable, not PII, length between 8 and 64 characters, and generated by your external system, such as your user\'s UUID, GUID, or SID. It can only contain dash (-) separated alphanumeric characters.
     */
    identity: string;
    /**
     * The unique SID identifier of the Challenge.
     */
    challengeSid: string;
    /**
     * The priority of the notification. For `push` Challenges it\'s always `high` which sends the notification immediately, and can wake up a sleeping device.
     */
    priority: string;
    /**
     * How long, in seconds, the notification is valid. Max: 5 minutes
     */
    ttl: number;
    /**
     * The date that this Notification was created, given in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
     */
    dateCreated: Date;
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON(): {
        sid: string;
        accountSid: string;
        serviceSid: string;
        entitySid: string;
        identity: string;
        challengeSid: string;
        priority: string;
        ttl: number;
        dateCreated: Date;
    };
    [inspect.custom](_depth: any, options: InspectOptions): string;
}
export {};
