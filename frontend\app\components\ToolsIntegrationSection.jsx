"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

const ToolsIntegrationSection = () => {
  const integrations = [
    {
      name: "Google Calendar",
      logo: "https://upload.wikimedia.org/wikipedia/commons/a/a5/Google_Calendar_icon_%282020%29.svg"
    },
    {
      name: "Outlook",
      logo: "https://upload.wikimedia.org/wikipedia/commons/d/df/Microsoft_Office_Outlook_%282018%E2%80%93present%29.svg"
    },
    {
      name: "Slack",
      logo: "https://upload.wikimedia.org/wikipedia/commons/d/d5/Slack_icon_2019.svg"
    },
    {
      name: "Salesforce",
      logo: "https://upload.wikimedia.org/wikipedia/commons/f/f9/Salesforce.com_logo.svg"
    },
    {
      name: "Zapier",
      logo: "https://cdn.worldvectorlogo.com/logos/zapier-1.svg"
    },
    {
      name: "<PERSON>b<PERSON><PERSON>",
      logo: "https://upload.wikimedia.org/wikipedia/commons/4/4d/HubSpot_Logo.svg"
    },
    {
      name: "Zoo<PERSON>",
      logo: "https://upload.wikimedia.org/wikipedia/commons/1/11/Zoom_Logo_2022.svg"
    },
    {
      name: "Shopify",
      logo: "https://upload.wikimedia.org/wikipedia/commons/0/0e/Shopify_logo_2018.svg"
    }
  ];

  return (
    <div className="container mx-auto py-16 px-4 text-white scroll-reveal relative z-10">
      {/* Modern ambient lighting effects */}
      <div className="absolute -top-40 right-20 w-[30rem] h-[30rem] rounded-full bg-blue-600/5 blur-[100px] animate-pulse-slow"></div>
      <div className="absolute -bottom-40 left-20 w-[30rem] h-[30rem] rounded-full bg-purple-600/5 blur-[100px] animate-pulse-slow-delay-2"></div>
      
      <div className="text-center max-w-4xl mx-auto mb-12">
        <h2 className="heading-lg mb-6 laser-gradient-text text-center" data-text="Tools & Integrations">
          Tools &amp; Integrations
        </h2>
        <p className="subheading-text">
          Seamlessly connects with the tools you already use, making it easy to incorporate CallSaver into your existing workflow without disruption.
        </p>
      </div>
      
      <div className="bg-white/5 backdrop-blur-md p-8 md:p-10 rounded-xl border border-purple-500/20 max-w-5xl mx-auto">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12">
          {integrations.map((integration, index) => (
            <div key={index} className="flex flex-col items-center justify-center">
              <div className="w-16 h-16 md:w-20 md:h-20 rounded-full bg-white flex items-center justify-center p-3 mb-4 hover:bg-white/90 transition-colors duration-300">
                <div className="relative w-full h-full">
                  <Image
                    src={integration.logo}
                    alt={`${integration.name} logo`}
                    fill
                    sizes="(max-width: 768px) 64px, 80px"
                    style={{ objectFit: 'contain' }}
                    quality={90}
                  />
                </div>
              </div>
              <span className="text-sm text-gray-300 text-center">{integration.name}</span>
            </div>
          ))}
        </div>
        
        <div className="mt-10 text-center">
          <p className="text-gray-300">
            The bigger your ecosystem, the more powerful CallSaver becomes. Connect with all your favorite platforms through our API or integrations.
          </p>
          <div className="mt-6">
            <Link href="#pricing" className="inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-full text-white bg-purple-600 hover:bg-purple-700 transition duration-300 ease-in-out transform hover:scale-105">
              See All Integrations
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolsIntegrationSection; 