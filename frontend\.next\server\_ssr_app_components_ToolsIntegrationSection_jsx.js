"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_app_components_ToolsIntegrationSection_jsx";
exports.ids = ["_ssr_app_components_ToolsIntegrationSection_jsx"];
exports.modules = {

/***/ "(ssr)/./app/components/ToolsIntegrationSection.jsx":
/*!****************************************************!*\
  !*** ./app/components/ToolsIntegrationSection.jsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ToolsIntegrationSection = ()=>{\n    const integrations = [\n        {\n            name: \"Google Calendar\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/a/a5/Google_Calendar_icon_%282020%29.svg\"\n        },\n        {\n            name: \"Outlook\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/d/df/Microsoft_Office_Outlook_%282018%E2%80%93present%29.svg\"\n        },\n        {\n            name: \"Slack\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/d/d5/Slack_icon_2019.svg\"\n        },\n        {\n            name: \"Salesforce\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/f/f9/Salesforce.com_logo.svg\"\n        },\n        {\n            name: \"Zapier\",\n            logo: \"https://cdn.worldvectorlogo.com/logos/zapier-1.svg\"\n        },\n        {\n            name: \"HubSpot\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/4/4d/HubSpot_Logo.svg\"\n        },\n        {\n            name: \"Zoom\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/1/11/Zoom_Logo_2022.svg\"\n        },\n        {\n            name: \"Shopify\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/0/0e/Shopify_logo_2018.svg\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-16 px-4 text-white scroll-reveal relative z-10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-40 right-20 w-[30rem] h-[30rem] rounded-full bg-blue-600/5 blur-[100px] animate-pulse-slow\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-40 left-20 w-[30rem] h-[30rem] rounded-full bg-purple-600/5 blur-[100px] animate-pulse-slow-delay-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-4xl mx-auto mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"heading-lg mb-6 laser-gradient-text text-center\",\n                        \"data-text\": \"Tools & Integrations\",\n                        children: \"Tools & Integrations\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"subheading-text\",\n                        children: \"Seamlessly connects with the tools you already use, making it easy to incorporate CallSaver into your existing workflow without disruption.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 backdrop-blur-md p-8 md:p-10 rounded-xl border border-purple-500/20 max-w-5xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12\",\n                        children: integrations.map((integration, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 md:w-20 md:h-20 rounded-full bg-white flex items-center justify-center p-3 mb-4 hover:bg-white/90 transition-colors duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: integration.logo,\n                                                alt: `${integration.name} logo`,\n                                                fill: true,\n                                                sizes: \"(max-width: 768px) 64px, 80px\",\n                                                style: {\n                                                    objectFit: \"contain\"\n                                                },\n                                                quality: 90\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-300 text-center\",\n                                        children: integration.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-10 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300\",\n                                children: \"The bigger your ecosystem, the more powerful CallSaver becomes. Connect with all your favorite platforms through our API or integrations.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"#pricing\",\n                                    className: \"inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-full text-white bg-purple-600 hover:bg-purple-700 transition duration-300 ease-in-out transform hover:scale-105\",\n                                    children: \"See All Integrations\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ToolsIntegrationSection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/ToolsIntegrationSection.jsx\n");

/***/ })

};
;