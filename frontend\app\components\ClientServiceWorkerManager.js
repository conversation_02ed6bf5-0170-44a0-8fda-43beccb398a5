"use client";

import { useEffect } from 'react';

export default function ClientServiceWorkerManager() {
  useEffect(() => {
    // Unregister service workers in development to prevent caching issues
    // and "Frame with ID was removed" errors during hot reloading.
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then((registrations) => {
        for (const registration of registrations) {
          registration.unregister();
        }
      }).catch((error) => {
        console.error('Service Worker unregistration failed:', error);
      });
    }
  }, []);

  // This component doesn't render anything
  return null;
}
