'use client';

import { usePathname } from 'next/navigation';
import { useEffect } from 'react';
import Navbar from './Navbar';

export default function ConditionalNavbar() {
  const pathname = usePathname();

  // Apply padding to main content only when navbar is visible
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (pathname && !pathname.startsWith('/dashboard')) {
        document.documentElement.style.setProperty('--main-padding-top', '7rem');
      } else {
        document.documentElement.style.setProperty('--main-padding-top', '0');
      }
    }
  }, [pathname]);

  // Don't show the navbar on dashboard pages
  if (pathname && pathname.startsWith('/dashboard')) {
    return null;
  }

  // Show navbar on non-dashboard pages
  return <Navbar />;
}