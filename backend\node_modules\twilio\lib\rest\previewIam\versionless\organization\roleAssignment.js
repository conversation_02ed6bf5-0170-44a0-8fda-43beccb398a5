"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleAssignmentPage = exports.RoleAssignmentListInstance = exports.RoleAssignmentInstance = exports.RoleAssignmentContextImpl = exports.PublicApiCreateRoleAssignmentRequest = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class PublicApiCreateRoleAssignmentRequest {
}
exports.PublicApiCreateRoleAssignmentRequest = PublicApiCreateRoleAssignmentRequest;
class RoleAssignmentContextImpl {
    constructor(_version, organizationSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(organizationSid)) {
            throw new Error("Parameter 'organizationSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { organizationSid, sid };
        this._uri = `/${organizationSid}/RoleAssignments/${sid}`;
    }
    remove(callback) {
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
            headers,
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.RoleAssignmentContextImpl = RoleAssignmentContextImpl;
class RoleAssignmentInstance {
    constructor(_version, payload, organizationSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.roleSid = payload.role_sid;
        this.scope = payload.scope;
        this.identity = payload.identity;
        this.code = payload.code;
        this.message = payload.message;
        this.moreInfo = payload.moreInfo;
        this.status = payload.status;
        this._solution = { organizationSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new RoleAssignmentContextImpl(this._version, this._solution.organizationSid, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a RoleAssignmentInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            roleSid: this.roleSid,
            scope: this.scope,
            identity: this.identity,
            code: this.code,
            message: this.message,
            moreInfo: this.moreInfo,
            status: this.status,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.RoleAssignmentInstance = RoleAssignmentInstance;
function RoleAssignmentListInstance(version, organizationSid) {
    if (!(0, utility_1.isValidPathParam)(organizationSid)) {
        throw new Error("Parameter 'organizationSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new RoleAssignmentContextImpl(version, organizationSid, sid);
    };
    instance._version = version;
    instance._solution = { organizationSid };
    instance._uri = `/${organizationSid}/RoleAssignments`;
    instance.create = function create(params, headers, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        let data = {};
        data = params;
        if (headers === null || headers === undefined) {
            headers = {};
        }
        headers["Content-Type"] = "application/json";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new RoleAssignmentInstance(operationVersion, payload, instance._solution.organizationSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params["identity"] !== undefined)
            data["Identity"] = params["identity"];
        if (params["scope"] !== undefined)
            data["Scope"] = params["scope"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new RoleAssignmentPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new RoleAssignmentPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.RoleAssignmentListInstance = RoleAssignmentListInstance;
class RoleAssignmentPage extends Page_1.default {
    /**
     * Initialize the RoleAssignmentPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of RoleAssignmentInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new RoleAssignmentInstance(this._version, payload, this._solution.organizationSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.RoleAssignmentPage = RoleAssignmentPage;
