"use client";

import { useState, useEffect, useCallback, memo } from 'react';
import { motion, useAnimation } from 'framer-motion';

/**
 * Debounce function to limit how often a function is called
 */
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * FallingIcons component - optimized for performance
 * Creates a background animation of falling icons/particles
 */
function FallingIcons({ count = 20, icons = ['✉️', '📱', '💬', '🔔', '📞', '🗓️', '📊', '💼'] }) {
  const [particles, setParticles] = useState([]);
  const [isPaused, setIsPaused] = useState(false);
  const controls = useAnimation();

  // Memoized helper function to detect low-performance devices
  const isLowPerformanceDevice = useCallback(() => {
    if (typeof window === 'undefined') return false;
    
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
    
    const hardwareConcurrency = navigator.hardwareConcurrency || 0;
    const isLowEndDevice = hardwareConcurrency > 0 && hardwareConcurrency <= 4;
    
    // Asynchronous battery check, not critical for initial render
    let isBatteryLow = false;
    if ('getBattery' in navigator) {
      navigator.getBattery().then(battery => {
        isBatteryLow = battery.level <= 0.2 && !battery.charging;
      }).catch(() => {}); // Handle potential errors
    }
    
    return isMobile || isLowEndDevice || isBatteryLow;
  }, []); // No dependencies, so it's created once

  // Memoized function to generate particles
  const generateParticles = useCallback((count, icons) => {
    const newParticles = [];
    const isLowPerformance = isLowPerformanceDevice();
    
    const adjustedCount = isLowPerformance ? Math.floor(count / 2) : count;
    const actualCount = typeof window !== 'undefined' ? Math.min(adjustedCount, Math.floor(window.innerWidth / 100)) : adjustedCount;
    
    for (let i = 0; i < actualCount; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 100,
        y: -10 - Math.random() * 100,
        size: isLowPerformance ? (12 + Math.random() * 12) : (16 + Math.random() * 24),
        duration: isLowPerformance ? (15 + Math.random() * 10) : (10 + Math.random() * 20),
        delay: Math.random() * 5,
        rotate: Math.random() * 360,
        icon: icons[Math.floor(Math.random() * icons.length)],
        opacity: isLowPerformance ? (0.1 + Math.random() * 0.2) : (0.1 + Math.random() * 0.3),
      });
    }
    return newParticles;
  }, [isLowPerformanceDevice]); // Depends on memoized isLowPerformanceDevice

  // Memoized scroll handler
  const handleScroll = useCallback(debounce(() => {
    setIsPaused(true);
    controls.stop();
    
    // Resume animations after scrolling stops
    setTimeout(() => {
      setIsPaused(false);
      controls.start();
    }, 200);
  }, 100), [controls]); // controls is a stable ref, so this is safe

  // Effect to handle page visibility changes and scroll
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleVisibilityChange = () => {
      if (document.hidden) {
        setIsPaused(true);
        controls.stop();
      } else {
        setIsPaused(false);
        controls.start();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [controls, handleScroll]); // Added handleScroll to dependencies

  // Generate particles on mount and when count/icons change, and handle resize
  useEffect(() => {
    // Only run on client
    if (typeof window === 'undefined') return;

    setParticles(generateParticles(count, icons));

    // Handle resize to regenerate particles
    const handleResize = debounce(() => {
      setParticles(generateParticles(count, icons));
    }, 200); // Debounce resize to prevent excessive updates

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [count, icons, generateParticles]); // Added generateParticles to dependencies

  if (particles.length === 0) return null;

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute text-white/30 select-none will-change-transform"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            fontSize: `${particle.size}px`,
          }}
          initial={{ 
            y: particle.y, 
            x: particle.x,
            rotate: particle.rotate,
            opacity: 0
          }}
          animate={isPaused ? {} : { 
            y: '120vh', 
            rotate: particle.rotate + (Math.random() > 0.5 ? 360 : -360),
            opacity: [0, particle.opacity, particle.opacity, 0]
          }}
          transition={{ 
            duration: particle.duration,
            delay: particle.delay,
            repeat: Infinity,
            ease: 'linear'
          }}
        >
          {particle.icon}
        </motion.div>
      ))}
    </div>
  );
}

// Memoize the component to prevent unnecessary re-renders
export default memo(FallingIcons);
