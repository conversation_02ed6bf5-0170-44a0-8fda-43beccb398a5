"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_TestimonialsSection_jsx"],{

/***/ "(app-pages-browser)/./app/components/TestimonialsSection.jsx":
/*!************************************************!*\
  !*** ./app/components/TestimonialsSection.jsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestimonialsSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* harmony import */ var _hooks_useWindowSize__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/useWindowSize */ \"(app-pages-browser)/./app/hooks/useWindowSize.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Testimonial data - can be moved to a separate file or fetched from API\nconst testimonials = [\n    {\n        id: 1,\n        name: \"Sarah Chen\",\n        role: \"Small Business Owner\",\n        avatar: \"/avatars/avatar-1.jpg\",\n        content: \"CallSaver's voice AI is like having a full-time receptionist at a fraction of the cost. Our customers are amazed when they realize they're talking to an AI—it sounds completely human and captures every detail perfectly.\",\n        stars: 5,\n        color: \"indigo\"\n    },\n    {\n        id: 2,\n        name: \"Marcus Johnson\",\n        role: \"Real Estate Agent\",\n        avatar: \"/avatars/avatar-2.jpg\",\n        content: \"The voice AI handles my after-hours calls flawlessly. I wake up to qualified leads, appointment bookings, and detailed call notes every morning. It's added over $15,000 in monthly commissions from leads I would have missed.\",\n        stars: 5,\n        color: \"purple\"\n    },\n    {\n        id: 3,\n        name: \"Elena Rodriguez\",\n        role: \"Healthcare Provider\",\n        avatar: \"/avatars/avatar-3.jpg\",\n        content: \"Our patients love the natural voice conversations with CallSaver. It handles appointment scheduling, insurance questions, and even pre-screening—all while sounding completely natural. We've reduced front desk staff by 60% and improved patient satisfaction.\",\n        stars: 5,\n        color: \"pink\"\n    }\n];\n// Color mapping for different testimonial cards\nconst colorMapping = {\n    indigo: {\n        bg: \"from-indigo-900/20 to-indigo-900/10\",\n        border: \"border-indigo-500/20\",\n        glow: \"group-hover:shadow-indigo-500/10\",\n        text: \"text-indigo-400\",\n        light: \"bg-indigo-500/10\",\n        lightBorder: \"border-indigo-500/20\"\n    },\n    purple: {\n        bg: \"from-purple-900/20 to-purple-900/10\",\n        border: \"border-purple-500/20\",\n        glow: \"group-hover:shadow-purple-500/10\",\n        text: \"text-purple-400\",\n        light: \"bg-purple-500/10\",\n        lightBorder: \"border-purple-500/20\"\n    },\n    pink: {\n        bg: \"from-pink-900/20 to-pink-900/10\",\n        border: \"border-pink-500/20\",\n        glow: \"group-hover:shadow-pink-500/10\",\n        text: \"text-pink-400\",\n        light: \"bg-pink-500/10\",\n        lightBorder: \"border-pink-500/20\"\n    }\n};\n// Rating stars component - moved outside to be accessible to TestimonialCard\nconst RatingStars = (param)=>{\n    let { rating } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex\",\n        children: [\n            ...Array(5)\n        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4 \".concat(i < rating ? \"text-yellow-400\" : \"text-gray-600\")\n            }, i, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, undefined);\n};\n_c = RatingStars;\n// Avatar component - also moved outside to be accessible to TestimonialCard\nconst Avatar = (param)=>{\n    let { testimonial } = param;\n    if (testimonial.avatar) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-12 h-12 rounded-full overflow-hidden border-2 border-white/10\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                src: testimonial.avatar,\n                alt: \"Profile photo of \".concat(testimonial.name, \", \").concat(testimonial.role),\n                className: \"w-full h-full object-cover\",\n                width: 48,\n                height: 48\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Use initials if no image\n    const colorClass = colorMapping[testimonial.color] || colorMapping.indigo;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-12 h-12 rounded-full flex items-center justify-center \".concat(colorClass.light, \" border \").concat(colorClass.lightBorder),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-sm font-semibold\",\n            children: testimonial.name.charAt(0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = Avatar;\nfunction TestimonialsSection() {\n    _s();\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isCarousel, setIsCarousel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check screen size to determine if we should use carousel\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMounted(true);\n        const handleResize = ()=>{\n            setIsCarousel(window.innerWidth < 1024);\n        };\n        // Initial check\n        handleResize();\n        // Add event listener\n        window.addEventListener(\"resize\", handleResize);\n        // Clean up\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    // Navigate through testimonials\n    const goToNext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setActiveIndex((current)=>current === testimonials.length - 1 ? 0 : current + 1);\n    }, []);\n    const goToPrevious = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setActiveIndex((current)=>current === 0 ? testimonials.length - 1 : current - 1);\n    }, []);\n    // Auto-rotate carousel\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isCarousel) return;\n        const interval = setInterval(goToNext, 6000);\n        return ()=>clearInterval(interval);\n    }, [\n        goToNext,\n        isCarousel\n    ]);\n    // If not mounted yet (for SSR)\n    if (!isMounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"testimonials\",\n        className: \"relative py-8 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-40 left-1/3 w-80 h-80 bg-indigo-600 rounded-full filter blur-[100px] opacity-10 animate-pulse-slow\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-40 right-1/4 w-80 h-80 bg-purple-600 rounded-full filter blur-[100px] opacity-10 animate-pulse-slow delay-1000\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"heading-lg laser-gradient-text mb-4\",\n                                \"data-text\": \"What Our Customers Say\",\n                                children: \"What Our Customers Say\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"subheading-text max-w-3xl mx-auto\",\n                                children: \"Join thousands of businesses who've transformed their communication with our human-like AI voice technology\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 gap-y-8\",\n                        children: testimonials.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialCard, {\n                                testimonial: testimonial\n                            }, testimonial.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(TestimonialsSection, \"OjFnuxqxA6Cig1UwmmcSbCcGsCg=\");\n_c2 = TestimonialsSection;\n// Testimonial Card Component\nfunction TestimonialCard(param) {\n    let { testimonial } = param;\n    const colorClass = colorMapping[testimonial.color] || colorMapping.indigo;\n    // Generate stars for rating\n    const stars = Array.from({\n        length: testimonial.stars\n    }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-5 h-5 text-yellow-400\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        }, i, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n            lineNumber: 191,\n            columnNumber: 5\n        }, this));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-lg border border-glow bg-gradient-to-br \".concat(colorClass.bg, \" p-6 h-full transition-all duration-300 hover:scale-[1.02] group relative overflow-hidden testimonial-card\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -right-20 -top-20 w-40 h-40 rounded-full \".concat(colorClass.light, \" blur-[80px] opacity-30 group-hover:opacity-60 transition-opacity duration-300\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex mb-4\",\n                children: stars\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 relative z-10 testimonial-content\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-300 mb-4\",\n                    children: [\n                        '\"',\n                        testimonial.content,\n                        '\"'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center testimonial-author\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 rounded-full \".concat(colorClass.light || \"bg-indigo-500/10\", \" border \").concat(colorClass.lightBorder || \"border-indigo-500/20\", \" flex items-center justify-center overflow-hidden\"),\n                            children: testimonial.color === \"indigo\" ? // Small Business Owner icon\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-6 w-6 text-indigo-400\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1.5,\n                                    d: \"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, this) : testimonial.color === \"purple\" ? // Real Estate Agent icon\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-6 w-6 text-purple-400\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1.5,\n                                    d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 220,\n                                columnNumber: 15\n                            }, this) : // Healthcare Provider icon\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"h-6 w-6 text-pink-400\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 1.5,\n                                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-white text-base\",\n                                children: testimonial.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"\".concat(colorClass.text, \" text-sm\"),\n                                children: testimonial.role\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-0 group-hover:opacity-100 shadow-[inset_0_0_30px_rgba(79,70,229,0.1)] transition-opacity duration-300 pointer-events-none \".concat(colorClass.glow)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\TestimonialsSection.jsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_c3 = TestimonialCard;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"RatingStars\");\n$RefreshReg$(_c1, \"Avatar\");\n$RefreshReg$(_c2, \"TestimonialsSection\");\n$RefreshReg$(_c3, \"TestimonialCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/TestimonialsSection.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/hooks/useWindowSize.js":
/*!************************************!*\
  !*** ./app/hooks/useWindowSize.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useWindowSize; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\nfunction useWindowSize() {\n    _s();\n    // Initialize with default values\n    const [windowSize, setWindowSize] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        width:  true ? window.innerWidth : 0,\n        height:  true ? window.innerHeight : 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Handler to call on window resize\n        function handleResize() {\n            // Set window width/height to state\n            setWindowSize({\n                width: window.innerWidth,\n                height: window.innerHeight\n            });\n        }\n        // Add event listener\n        window.addEventListener(\"resize\", handleResize);\n        // Call handler right away so state gets updated with initial window size\n        handleResize();\n        // Remove event listener on cleanup\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []); // Empty array ensures that effect is only run on mount and unmount\n    return windowSize;\n}\n_s(useWindowSize, \"eL5Q34K+VyjhfzaxHyc9sUqlMhE=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/hooks/useWindowSize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js":
/*!****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/solid/esm/StarIcon.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction StarIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z\",\n        clipRule: \"evenodd\"\n    }));\n}\n_c = StarIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(StarIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"StarIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\n"));

/***/ })

}]);