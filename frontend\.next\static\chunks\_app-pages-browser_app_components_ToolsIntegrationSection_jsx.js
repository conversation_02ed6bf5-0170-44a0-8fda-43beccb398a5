"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_ToolsIntegrationSection_jsx"],{

/***/ "(app-pages-browser)/./app/components/ToolsIntegrationSection.jsx":
/*!****************************************************!*\
  !*** ./app/components/ToolsIntegrationSection.jsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ToolsIntegrationSection = ()=>{\n    const integrations = [\n        {\n            name: \"Google Calendar\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/a/a5/Google_Calendar_icon_%282020%29.svg\"\n        },\n        {\n            name: \"Outlook\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/d/df/Microsoft_Office_Outlook_%282018%E2%80%93present%29.svg\"\n        },\n        {\n            name: \"Slack\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/d/d5/Slack_icon_2019.svg\"\n        },\n        {\n            name: \"Salesforce\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/f/f9/Salesforce.com_logo.svg\"\n        },\n        {\n            name: \"Zapier\",\n            logo: \"https://cdn.worldvectorlogo.com/logos/zapier-1.svg\"\n        },\n        {\n            name: \"HubSpot\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/4/4d/HubSpot_Logo.svg\"\n        },\n        {\n            name: \"Zoom\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/1/11/Zoom_Logo_2022.svg\"\n        },\n        {\n            name: \"Shopify\",\n            logo: \"https://upload.wikimedia.org/wikipedia/commons/0/0e/Shopify_logo_2018.svg\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-16 px-4 text-white scroll-reveal relative z-10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-40 right-20 w-[30rem] h-[30rem] rounded-full bg-blue-600/5 blur-[100px] animate-pulse-slow\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-40 left-20 w-[30rem] h-[30rem] rounded-full bg-purple-600/5 blur-[100px] animate-pulse-slow-delay-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-4xl mx-auto mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"heading-lg mb-6 laser-gradient-text text-center\",\n                        \"data-text\": \"Tools & Integrations\",\n                        children: \"Tools & Integrations\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"subheading-text\",\n                        children: \"Seamlessly connects with the tools you already use, making it easy to incorporate CallSaver into your existing workflow without disruption.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/5 backdrop-blur-md p-8 md:p-10 rounded-xl border border-purple-500/20 max-w-5xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12\",\n                        children: integrations.map((integration, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 md:w-20 md:h-20 rounded-full bg-white flex items-center justify-center p-3 mb-4 hover:bg-white/90 transition-colors duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative w-full h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: integration.logo,\n                                                alt: \"\".concat(integration.name, \" logo\"),\n                                                fill: true,\n                                                sizes: \"(max-width: 768px) 64px, 80px\",\n                                                style: {\n                                                    objectFit: \"contain\"\n                                                },\n                                                quality: 90\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-300 text-center\",\n                                        children: integration.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-10 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300\",\n                                children: \"The bigger your ecosystem, the more powerful CallSaver becomes. Connect with all your favorite platforms through our API or integrations.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"#pricing\",\n                                    className: \"inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-full text-white bg-purple-600 hover:bg-purple-700 transition duration-300 ease-in-out transform hover:scale-105\",\n                                    children: \"See All Integrations\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\callsaver.app\\\\frontend\\\\app\\\\components\\\\ToolsIntegrationSection.jsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ToolsIntegrationSection;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ToolsIntegrationSection);\nvar _c;\n$RefreshReg$(_c, \"ToolsIntegrationSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ToolsIntegrationSection.jsx\n"));

/***/ })

}]);